import express from 'express';
import <PERSON><PERSON> from 'joi';
import { tronWeb } from '../config/tron.js';
import logger from '../utils/logger.js';

const router = express.Router();
const USDT_CONTRACT = process.env.USDT_CONTRACT_ADDRESS;

const schema = Joi.object({
  toAddress: Joi.string().required(),
  amount: Joi.number().positive().required()
});

// POST /deposit: initiate on-chain TRC20 transfer from main wallet
router.post('/', async (req, res) => {
  const { error, value } = schema.validate(req.body);
  if (error) return res.status(400).json({ error: error.details[0].message });
  const { toAddress, amount } = value;
  try {
    const contract = await tronWeb.contract().at(USDT_CONTRACT);
    const amountSun = tronWeb.toSun(amount);
    const txID = await contract.transfer(toAddress, amountSun).send();
    res.json({ txID, amount });
  } catch (err) {
    logger.error('Deposit error', { error: err.message });
    res.status(500).json({ error: 'Failed to make deposit', details: err.message });
  }
});

export default router;