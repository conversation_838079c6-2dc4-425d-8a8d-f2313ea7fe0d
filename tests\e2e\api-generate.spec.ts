import { test, expect } from '@playwright/test';

test.describe('Generate USDT Issuance API', () => {
  const validApiKey = process.env.API_KEY || 'b6a9331327e44fdfa6c2ef8872b3c1f0a7d4c9e5f1b2a3c4d5e6f7a8b9c0d1e2';

  test.describe('Input validation', () => {
    test('should reject empty request body', async ({ request }) => {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {}
      });

      expect(response.status()).toBe(400);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
    });

    test('should reject missing toAddress', async ({ request }) => {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          amount: 1.0,
          validityMinutes: 10
        }
      });

      expect(response.status()).toBe(400);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
      expect(body.error).toContain('toAddress');
    });

    test('should reject missing amount', async ({ request }) => {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          toAddress: 'TADDR123',
          validityMinutes: 10
        }
      });

      expect(response.status()).toBe(400);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
      expect(body.error).toContain('amount');
    });

    test('should reject missing validityMinutes', async ({ request }) => {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          toAddress: 'TADDR123',
          amount: 1.0
        }
      });

      expect(response.status()).toBe(400);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
      expect(body.error).toContain('validityMinutes');
    });

    test('should reject empty toAddress', async ({ request }) => {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          toAddress: '',
          amount: 1.0,
          validityMinutes: 10
        }
      });

      expect(response.status()).toBe(400);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
    });

    test('should reject negative amount', async ({ request }) => {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          toAddress: 'TADDR123',
          amount: -1.0,
          validityMinutes: 10
        }
      });

      expect(response.status()).toBe(400);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
      expect(body.error).toContain('positive');
    });

    test('should reject zero amount', async ({ request }) => {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          toAddress: 'TADDR123',
          amount: 0,
          validityMinutes: 10
        }
      });

      expect(response.status()).toBe(400);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
      expect(body.error).toContain('positive');
    });

    test('should reject validityMinutes below minimum', async ({ request }) => {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          toAddress: 'TADDR123',
          amount: 1.0,
          validityMinutes: 4
        }
      });

      expect(response.status()).toBe(400);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
    });

    test('should reject validityMinutes above maximum', async ({ request }) => {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          toAddress: 'TADDR123',
          amount: 1.0,
          validityMinutes: 172801
        }
      });

      expect(response.status()).toBe(400);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
    });
  });

  test.describe('Successful issuance generation', () => {
    test('should successfully generate issuance with valid data', async ({ request }) => {
      const testData = {
        toAddress: 'TADDR123',
        amount: 0.5,
        validityMinutes: 10
      };

      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: testData
      });

      // Note: This test assumes the service is properly configured and connected
      // In a real environment, this would return 200, but may return 500 if TRON is not accessible
      if (response.status() === 200) {
        const body = await response.json();
        expect(body).toHaveProperty('txID');
        expect(body).toHaveProperty('amount');
        expect(body).toHaveProperty('expiresAt');
        expect(body.amount).toBe(testData.amount);
        expect(typeof body.txID).toBe('string');
        expect(body.txID.length).toBeGreaterThan(0);
        
        // Verify expiresAt is a valid ISO date string
        expect(() => new Date(body.expiresAt)).not.toThrow();
      } else if (response.status() === 500) {
        // Service configuration issue (expected in test environment)
        const body = await response.json();
        expect(body).toHaveProperty('error');
      } else {
        // Any other status code is unexpected
        throw new Error(`Unexpected status code: ${response.status()}`);
      }
    });

    test('should handle minimum valid parameters', async ({ request }) => {
      const testData = {
        toAddress: 'TADDR123',
        amount: 0.01,
        validityMinutes: 5
      };

      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: testData
      });

      // Should not be a validation error (400)
      expect(response.status()).not.toBe(400);
      expect([200, 500]).toContain(response.status());
    });

    test('should handle maximum valid parameters', async ({ request }) => {
      const testData = {
        toAddress: 'TADDR123',
        amount: 1000.0,
        validityMinutes: 172800
      };

      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: testData
      });

      // Should not be a validation error (400)
      expect(response.status()).not.toBe(400);
      expect([200, 500]).toContain(response.status());
    });
  });
});