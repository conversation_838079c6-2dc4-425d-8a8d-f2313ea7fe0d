import express from 'express';
import { tronWeb } from '../config/tron.js';
import redisClient from '../config/redis.js';

const router = express.Router();

router.get('/', async (req, res) => {
  try {
    const redisPing = await redisClient.ping();
    const tronConnected = await tronWeb.isConnected();
    res.json({ redis: redisPing, tron: tronConnected });
  } catch (error) {
    res.status(500).json({ error: 'Service Unavailable', details: error.message });
  }
});

export default router;