import express from 'express';
import <PERSON><PERSON> from 'joi';
import { tronWeb, mainAddress } from '../config/tron.js';
import redisClient from '../config/redis.js';
import logger from '../utils/logger.js';

const router = express.Router();

const USDT_CONTRACT = process.env.USDT_CONTRACT_ADDRESS;
const RESERVE_RATIO = 50000; // 1 USDT on-chain backs 50,000 USDT off-chain
const REDEMPTION_FEE_PERCENT = 0.5; // 0.5% fee for redemption
const MIN_REDEMPTION_AMOUNT = 50000; // Minimum 50,000 off-chain USDT to redeem 1 real USDT

// Schema for redemption request
const redeemSchema = Joi.object({
  fromAddress: Joi.string().required(),
  toAddress: Joi.string().required(),
  offChainAmount: Joi.number().positive().min(MIN_REDEMPTION_AMOUNT).required()
});

// Helper function to get available off-chain balance
async function getAvailableBalance(address) {
  try {
    const keys = await redisClient.keys(`offchain:${address}:*`);
    let balance = 0;
    
    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.status === 'active') {
          balance += data.netAmount || data.amount;
        }
      }
    }
    
    return balance;
  } catch (error) {
    logger.error('Error getting available balance', { error: error.message, address });
    return 0;
  }
}

// Helper function to deduct off-chain balance
async function deductOffChainBalance(fromAddress, amount) {
  try {
    const keys = await redisClient.keys(`offchain:${fromAddress}:*`);
    let remainingToDeduct = amount;
    const deductedFrom = [];
    
    // Sort by expiration date (deduct from expiring first)
    const records = [];
    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.status === 'active' && (data.netAmount || data.amount) > 0) {
          records.push({ key, data });
        }
      }
    }
    
    records.sort((a, b) => new Date(a.data.expiresAt) - new Date(b.data.expiresAt));
    
    // Deduct from oldest expiring records first
    for (const { key, data } of records) {
      if (remainingToDeduct <= 0) break;
      
      const availableAmount = data.netAmount || data.amount;
      const deductAmount = Math.min(availableAmount, remainingToDeduct);
      
      if (deductAmount === availableAmount) {
        // Fully consumed this record
        data.status = 'redeemed';
        data.redeemedAt = new Date().toISOString();
      } else {
        // Partially consumed
        data.netAmount = availableAmount - deductAmount;
        data.partiallyRedeemed = true;
        data.lastRedemptionAt = new Date().toISOString();
      }
      
      await redisClient.setEx(key, Math.floor((new Date(data.expiresAt) - new Date()) / 1000), JSON.stringify(data));
      
      deductedFrom.push({
        txID: data.txID,
        deductedAmount: deductAmount,
        remainingAmount: data.netAmount || 0
      });
      
      remainingToDeduct -= deductAmount;
    }
    
    return { success: remainingToDeduct === 0, deductedFrom };
  } catch (error) {
    logger.error('Error deducting off-chain balance', { error: error.message, fromAddress, amount });
    return { success: false, deductedFrom: [] };
  }
}

// Helper function to generate redemption transaction ID
function generateRedemptionTxID() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `REDEEM_${timestamp}_${random}`;
}

// POST /redeem - Redeem off-chain USDT for real on-chain USDT
router.post('/', async (req, res) => {
  const { error, value } = redeemSchema.validate(req.body);
  if (error) return res.status(400).json({ error: error.details[0].message });

  const { fromAddress, toAddress, offChainAmount } = value;

  try {
    // Validate TRON addresses
    if (!tronWeb.isAddress(fromAddress)) {
      return res.status(400).json({ error: 'Invalid fromAddress format' });
    }
    if (!tronWeb.isAddress(toAddress)) {
      return res.status(400).json({ error: 'Invalid toAddress format' });
    }

    // Check sender's available off-chain balance
    const senderBalance = await getAvailableBalance(fromAddress);
    if (senderBalance < offChainAmount) {
      return res.status(400).json({
        error: `Insufficient off-chain balance. Available: ${senderBalance} USDT, Requested: ${offChainAmount} USDT`
      });
    }

    // Calculate real USDT amount (fractional conversion)
    const realUsdtAmount = offChainAmount / RESERVE_RATIO;
    const redemptionFee = realUsdtAmount * (REDEMPTION_FEE_PERCENT / 100);
    const netRealUsdt = realUsdtAmount - redemptionFee;

    // Check if we have enough on-chain USDT for redemption
    const contract = await tronWeb.contract().at(USDT_CONTRACT);
    const rawBalance = await contract.balanceOf(mainAddress).call();
    const onChainReserve = parseFloat(rawBalance.toString()) / 1000000;

    if (onChainReserve < realUsdtAmount) {
      return res.status(400).json({
        error: `Insufficient on-chain reserve for redemption. Required: ${realUsdtAmount} USDT, Available: ${onChainReserve} USDT`
      });
    }

    // Deduct off-chain balance from sender
    const deductionResult = await deductOffChainBalance(fromAddress, offChainAmount);
    if (!deductionResult.success) {
      return res.status(500).json({ error: 'Failed to deduct off-chain balance' });
    }

    // Send real USDT to the target address
    const amountSun = Math.floor(netRealUsdt * 1000000); // Convert to 6 decimals
    const txID = await contract.transfer(toAddress, amountSun).send();

    // Generate redemption record
    const redemptionTxID = generateRedemptionTxID();
    const redeemedAt = new Date().toISOString();

    const redemptionRecord = {
      redemptionTxID,
      onChainTxID: txID,
      type: 'redemption',
      fromAddress,
      toAddress,
      offChainAmount,
      realUsdtAmount,
      netRealUsdt,
      redemptionFee,
      status: 'completed',
      redeemedAt,
      deductedFrom: deductionResult.deductedFrom,
      reserveRatio: RESERVE_RATIO
    };

    // Store redemption record
    const redemptionKey = `redemption:${redemptionTxID}`;
    await redisClient.setEx(redemptionKey, 365 * 24 * 60 * 60, JSON.stringify(redemptionRecord)); // 1 year

    logger.info('Off-chain USDT redemption completed', {
      redemptionTxID,
      onChainTxID: txID,
      fromAddress,
      toAddress,
      offChainAmount,
      realUsdtAmount,
      netRealUsdt
    });

    res.json({
      success: true,
      redemptionTxID,
      onChainTxID: txID,
      type: 'redemption',
      fromAddress,
      toAddress,
      offChainAmount,
      realUsdtAmount,
      netRealUsdt,
      redemptionFee: `${redemptionFee} USDT (${REDEMPTION_FEE_PERCENT}%)`,
      status: 'completed',
      redeemedAt,
      conversion: {
        rate: `${RESERVE_RATIO}:1 (off-chain:on-chain)`,
        description: `${offChainAmount} off-chain USDT → ${netRealUsdt} real USDT`
      },
      note: "Real USDT has been sent to the specified address on the TRON blockchain"
    });

  } catch (err) {
    logger.error('Redemption error', { error: err.message, stack: err.stack });
    res.status(500).json({ error: 'Failed to process redemption', details: err.message });
  }
});

// GET /redeem/history/:address - Get redemption history for an address
router.get('/history/:address', async (req, res) => {
  try {
    const { address } = req.params;
    
    if (!tronWeb.isAddress(address)) {
      return res.status(400).json({ error: 'Invalid TRON address format' });
    }

    const keys = await redisClient.keys('redemption:*');
    const redemptions = [];

    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.fromAddress === address || data.toAddress === address) {
          redemptions.push(data);
        }
      }
    }

    redemptions.sort((a, b) => new Date(b.redeemedAt) - new Date(a.redeemedAt));

    res.json({
      address,
      redemptionCount: redemptions.length,
      redemptions
    });

  } catch (err) {
    logger.error('Redemption history error', { error: err.message });
    res.status(500).json({ error: 'Failed to fetch redemption history', details: err.message });
  }
});

export default router;
