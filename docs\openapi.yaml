openapi: 3.0.0
info:
  title: USDT-TRC20 Issuance Service
  version: 1.0.0
servers:
  - url: http://localhost:{port}
    variables:
      port:
        default: "3000"
paths:
  /health:
    get:
      summary: Service health check
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  redis:
                    type: string
                  tron:
                    type: boolean
  /generate:
    post:
      summary: Generate USDT issuance transaction
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                toAddress:
                  type: string
                amount:
                  type: number
                validityMinutes:
                  type: integer
              required: [toAddress, amount, validityMinutes]
      responses:
        '200':
          description: issuance created
          content:
            application/json:
              schema:
                type: object
                properties:
                  txID:
                    type: string
                  amount:
                    type: number
                  expiresAt:
                    type: string
        '400':
          description: Bad Request
  /status/{address}:
    get:
      summary: Get issuance status for an address
      parameters:
        - in: path
          name: address
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of issuances
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    txID:
                      type: string
                    amount:
                      type: number
                    expiresAt:
                      type: string
