import 'dotenv/config';
import { TronWeb } from 'tronweb';
import bip39 from 'bip39';
import hdkey from 'hdkey';
import { getNetworkConfig, initializeNetwork } from './network.js';

// Initialize network configuration
const networkConfig = initializeNetwork();
const { fullNode, solidityNode, eventNode, usdtContract } = networkConfig;

// Function to derive private key from mnemonic using BIP44 path m/44'/195'/0'/0/1
async function derivePrivateKeyFromMnemonic(mnemonic, accountIndex = 1) {
  if (!bip39.validateMnemonic(mnemonic)) {
    throw new Error('Invalid mnemonic phrase');
  }
  const seed = await bip39.mnemonicToSeed(mnemonic);
  const root = hdkey.fromMasterSeed(seed);
  const derivationPath = `m/44'/195'/0'/0/${accountIndex}`;
  const addrNode = root.derive(derivationPath);
  return addrNode.privateKey.toString('hex');
}

// Initialize TronWeb configuration
async function initializeTronWeb() {
  // Determine private key source
  let privateKey;
  const directPrivateKey = process.env.MAIN_WALLET_PRIVATE_KEY;
  const mnemonic = process.env.MAIN_WALLET_MNEMONIC;
  const accountIndex = parseInt(process.env.MAIN_WALLET_ACCOUNT_INDEX || '0', 10);

  if (mnemonic) {
    // Use mnemonic with BIP44 derivation path m/44'/195'/0'/0/1 (or specified index)
    privateKey = await derivePrivateKeyFromMnemonic(mnemonic, accountIndex);
    console.log(`Using BIP44 derivation path: m/44'/195'/0'/0/${accountIndex}`);
  } else if (directPrivateKey) {
    // Use direct private key
    privateKey = directPrivateKey;
    console.log('Using direct private key from environment');
  } else {
    throw new Error('Either MAIN_WALLET_PRIVATE_KEY or MAIN_WALLET_MNEMONIC must be provided');
  }

  // Initialize real TronWeb instance
  const tronWeb = new TronWeb(fullNode, solidityNode, eventNode, privateKey);
  const mainAddress = tronWeb.defaultAddress.base58;

  return { tronWeb, mainAddress };
}

// Initialize and export
const { tronWeb, mainAddress } = await initializeTronWeb();
export { tronWeb, mainAddress, networkConfig };