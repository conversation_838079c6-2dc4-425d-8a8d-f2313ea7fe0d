import 'dotenv/config';
import express from 'express';
import rateLimit from 'express-rate-limit';
import swaggerUi from 'swagger-ui-express';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import morgan from 'morgan';
import { v4 as uuidv4 } from 'uuid';
import logger from './utils/logger.js';
import apiKeyAuth from './middleware/apiKeyAuth.js';
import healthRoute from './routes/health.js';
import generateRoute from './routes/generate.js';
import statusRoute from './routes/status.js';
import balanceRoute from './routes/balance.js';
import depositRoute from './routes/deposit.js';
import offchainBalanceRoute from './routes/offchain-balance.js';
import offchainTransferRoute from './routes/offchain-transfer.js';
import reserveStatusRoute from './routes/reserve-status.js';
import redeemRoute from './routes/redeem.js';
import voucherRoute from './routes/voucher.js';
import externalWalletRoute from './routes/external-wallet.js';
import tokenInfoRoute from './routes/token-info.js';
import networkRoute from './routes/network.js';

// Resolve __dirname in ES module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load Swagger spec from JSON file
const openapiPath = path.resolve(__dirname, '..', 'docs', 'openapi.json');
const openapi = JSON.parse(fs.readFileSync(openapiPath, 'utf-8'));

const app = express();
app.use(express.json());

// Request ID middleware
app.use((req, res, next) => {
  req.id = uuidv4();
  next();
});

// Logging
app.use(morgan('combined', { stream: logger.stream }));

// Rate limiting - More permissive for testing
const limiter = rateLimit({ 
  windowMs: 60 * 1000, // 1 minute window
  max: 1000, // Allow 1000 requests per minute for testing
  skip: (req) => req.path === '/health' // Skip rate limiting for health endpoint
});
app.use(limiter);

// Swagger UI
app.use('/docs', swaggerUi.serve, swaggerUi.setup(openapi));

// Serve a simple front-end for easy testing
app.use(express.static(path.resolve(__dirname, '../public')));

// Routes and middleware
app.use('/health', healthRoute);
app.use(apiKeyAuth);
app.use('/generate', generateRoute);
app.use('/status', statusRoute);
app.use('/balance', balanceRoute);
app.use('/deposit', depositRoute);
app.use('/offchain-balance', offchainBalanceRoute);
app.use('/offchain-transfer', offchainTransferRoute);
app.use('/reserve-status', reserveStatusRoute);
app.use('/redeem', redeemRoute);
app.use('/voucher', voucherRoute);
app.use('/external-wallet', externalWalletRoute);
app.use('/token-info', tokenInfoRoute);
app.use('/network', networkRoute);

const PORT = process.env.PORT || 3000;

// Start server if run directly
if (process.argv[1] === __filename) {
  app.listen(PORT, () => {
    logger.info(`Server ${process.env.npm_package_name} listening on port ${PORT}`);
  });
}

export default app;
