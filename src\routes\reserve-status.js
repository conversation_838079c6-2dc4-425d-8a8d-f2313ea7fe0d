import express from 'express';
import { tronWeb, mainAddress } from '../config/tron.js';
import redisClient from '../config/redis.js';
import logger from '../utils/logger.js';

const router = express.Router();

const USDT_CONTRACT = process.env.USDT_CONTRACT_ADDRESS;
const RESERVE_RATIO = 50000; // 1 USDT on-chain backs 50,000 USDT off-chain
const MIN_RESERVE_AMOUNT = 1;

// Helper function to get total off-chain issuances
async function getTotalOffChainIssuances() {
  try {
    const keys = await redisClient.keys('offchain:*');
    let total = 0;
    let activeCount = 0;
    const breakdown = {};
    
    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.status === 'active') {
          const amount = data.netAmount || data.amount;
          total += amount;
          activeCount++;
          
          // Breakdown by address
          const address = data.toAddress;
          if (!breakdown[address]) {
            breakdown[address] = { balance: 0, transactions: 0 };
          }
          breakdown[address].balance += amount;
          breakdown[address].transactions++;
        }
      }
    }
    
    return { total, activeCount, breakdown };
  } catch (error) {
    logger.error('Error calculating total off-chain issuances', { error: error.message });
    return { total: 0, activeCount: 0, breakdown: {} };
  }
}

// GET /reserve-status - Get comprehensive reserve and off-chain status
router.get('/', async (req, res) => {
  try {
    // Get on-chain USDT reserve balance
    const contract = await tronWeb.contract().at(USDT_CONTRACT);
    const rawBalance = await contract.balanceOf(mainAddress).call();
    const onChainReserve = parseFloat(rawBalance.toString()) / 1000000; // Convert to USDT (6 decimals)
    
    // Get TRX balance for gas fees
    const trxBalance = await tronWeb.trx.getBalance(mainAddress);
    const trxBalanceFormatted = tronWeb.fromSun(trxBalance);
    
    // Get off-chain issuance data
    const { total: totalOffChain, activeCount, breakdown } = await getTotalOffChainIssuances();
    
    // Calculate reserve metrics
    const maxCapacity = onChainReserve * RESERVE_RATIO;
    const utilizationRate = maxCapacity > 0 ? (totalOffChain / maxCapacity) * 100 : 0;
    const remainingCapacity = maxCapacity - totalOffChain;
    const actualReserveRatio = totalOffChain > 0 ? totalOffChain / onChainReserve : 0;
    
    // Health checks
    const healthChecks = {
      sufficientReserve: onChainReserve >= MIN_RESERVE_AMOUNT,
      withinCapacity: totalOffChain <= maxCapacity,
      hasGasForTransactions: trxBalanceFormatted > 10, // At least 10 TRX for gas
      utilizationHealthy: utilizationRate < 90 // Less than 90% utilization
    };
    
    const overallHealth = Object.values(healthChecks).every(check => check);
    
    // Risk assessment
    let riskLevel = 'LOW';
    if (utilizationRate > 90) riskLevel = 'HIGH';
    else if (utilizationRate > 70) riskLevel = 'MEDIUM';
    
    // Top holders
    const topHolders = Object.entries(breakdown)
      .map(([address, data]) => ({ address, ...data }))
      .sort((a, b) => b.balance - a.balance)
      .slice(0, 10);
    
    res.json({
      timestamp: new Date().toISOString(),
      wallet: {
        address: mainAddress,
        trxBalance: trxBalanceFormatted,
        usdtReserve: onChainReserve
      },
      reserve: {
        onChainReserve: `${onChainReserve} USDT`,
        minRequiredReserve: `${MIN_RESERVE_AMOUNT} USDT`,
        reserveRatio: `1:${RESERVE_RATIO}`,
        maxOffChainCapacity: `${maxCapacity} USDT`,
        currentOffChainIssued: `${totalOffChain} USDT`,
        remainingCapacity: `${remainingCapacity} USDT`,
        utilizationRate: `${utilizationRate.toFixed(2)}%`,
        actualReserveRatio: `1:${actualReserveRatio.toFixed(0)}`
      },
      offChain: {
        totalIssued: totalOffChain,
        activeTransactions: activeCount,
        uniqueHolders: Object.keys(breakdown).length,
        averageHolding: Object.keys(breakdown).length > 0 ? totalOffChain / Object.keys(breakdown).length : 0
      },
      health: {
        overall: overallHealth ? 'HEALTHY' : 'UNHEALTHY',
        riskLevel,
        checks: {
          sufficientReserve: healthChecks.sufficientReserve ? 'PASS' : 'FAIL',
          withinCapacity: healthChecks.withinCapacity ? 'PASS' : 'FAIL',
          hasGasForTransactions: healthChecks.hasGasForTransactions ? 'PASS' : 'FAIL',
          utilizationHealthy: healthChecks.utilizationHealthy ? 'PASS' : 'FAIL'
        }
      },
      topHolders,
      warnings: [
        ...(onChainReserve < MIN_RESERVE_AMOUNT ? ['On-chain reserve below minimum requirement'] : []),
        ...(utilizationRate > 90 ? ['High utilization rate - approaching capacity limit'] : []),
        ...(trxBalanceFormatted < 10 ? ['Low TRX balance - may affect transaction processing'] : []),
        ...(totalOffChain > maxCapacity ? ['Off-chain issuances exceed reserve capacity'] : [])
      ]
    });
    
  } catch (err) {
    logger.error('Reserve status error', { error: err.message });
    res.status(500).json({ error: 'Failed to fetch reserve status', details: err.message });
  }
});

export default router;
