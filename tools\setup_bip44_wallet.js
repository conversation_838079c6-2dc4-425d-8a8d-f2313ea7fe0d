#!/usr/bin/env node
import 'dotenv/config';
import { TronWeb } from 'tronweb';
import bip39 from 'bip39';
import hdkey from 'hdkey';

// Function to derive private key from mnemonic using BIP44 path
async function derivePrivateKeyFromMnemonic(mnemonic, accountIndex = 0) {
  if (!bip39.validateMnemonic(mnemonic)) {
    throw new Error('Invalid mnemonic phrase');
  }
  const seed = await bip39.mnemonicToSeed(mnemonic);
  const root = hdkey.fromMasterSeed(seed);
  const derivationPath = `m/44'/195'/0'/0/${accountIndex}`;
  const addrNode = root.derive(derivationPath);
  return {
    privateKey: addrNode.privateKey.toString('hex'),
    derivationPath
  };
}

// Function to generate a new mnemonic and derive wallet at index 1
function generateNewWallet() {
  const mnemonic = bip39.generateMnemonic();
  return { mnemonic };
}

// Function to get address from private key
function getAddressFromPrivateKey(privateKey) {
  return TronWeb.address.fromPrivateKey(privateKey);
}

async function main() {
  const [,, command, ...args] = process.argv;

  switch (command) {
    case 'generate':
      // Generate a new mnemonic and show wallet at index 0
      const { mnemonic } = generateNewWallet();
      const { privateKey, derivationPath } = await derivePrivateKeyFromMnemonic(mnemonic, 0);
      const address = getAddressFromPrivateKey(privateKey);

      console.log('🔐 New BIP44 Wallet Generated');
      console.log('================================');
      console.log('Mnemonic:', mnemonic);
      console.log('Derivation Path:', derivationPath);
      console.log('Private Key:', privateKey);
      console.log('Address:', address);
      console.log('');
      console.log('📝 To use this wallet, add to your .env file:');
      console.log('MAIN_WALLET_MNEMONIC="' + mnemonic + '"');
      console.log('MAIN_WALLET_ACCOUNT_INDEX=0');
      console.log('');
      console.log('⚠️  Remove or comment out MAIN_WALLET_PRIVATE_KEY');
      break;

    case 'find-target':
      // Try to find a mnemonic that generates the target address at index 1
      const targetAddress = args[0];
      if (!targetAddress) {
        console.error('❌ Please provide target address');
        console.log('Usage: node tools/setup_bip44_wallet.js find-target <address>');
        process.exit(1);
      }

      console.log('🔍 Searching for mnemonic that generates target address...');
      console.log('Target Address:', targetAddress);
      console.log('This may take a while...');

      let found = false;
      let attempts = 0;
      const maxAttempts = 10000; // Limit search to prevent infinite loop

      while (!found && attempts < maxAttempts) {
        const testMnemonic = bip39.generateMnemonic();
        const { privateKey: testKey } = await derivePrivateKeyFromMnemonic(testMnemonic, 1);
        const testAddress = getAddressFromPrivateKey(testKey);

        attempts++;
        if (attempts % 1000 === 0) {
          console.log(`Tried ${attempts} mnemonics...`);
        }

        if (testAddress === targetAddress) {
          console.log('');
          console.log('🎉 Found matching mnemonic!');
          console.log('================================');
          console.log('Mnemonic:', testMnemonic);
          console.log('Derivation Path: m/44\'/195\'/0\'/0/1');
          console.log('Private Key:', testKey);
          console.log('Address:', testAddress);
          console.log('');
          console.log('📝 To use this wallet, add to your .env file:');
          console.log('MAIN_WALLET_MNEMONIC="' + testMnemonic + '"');
          console.log('MAIN_WALLET_ACCOUNT_INDEX=1');
          found = true;
        }
      }

      if (!found) {
        console.log('');
        console.log('❌ Could not find matching mnemonic after', maxAttempts, 'attempts');
        console.log('💡 Consider using a different approach:');
        console.log('   1. Generate a new mnemonic and use its address');
        console.log('   2. Use the direct private key for your target address');
      }
      break;

    case 'derive':
      // Derive wallet from provided mnemonic
      const [inputMnemonic, accountIndex] = args;
      if (!inputMnemonic) {
        console.error('❌ Please provide a mnemonic phrase');
        console.log('Usage: node tools/setup_bip44_wallet.js derive "your twelve word mnemonic phrase" [accountIndex]');
        process.exit(1);
      }
      
      const index = accountIndex ? parseInt(accountIndex, 10) : 0;
      try {
        const result = await derivePrivateKeyFromMnemonic(inputMnemonic, index);
        const derivedAddress = getAddressFromPrivateKey(result.privateKey);
        
        console.log('🔍 BIP44 Wallet Derivation');
        console.log('==========================');
        console.log('Mnemonic:', inputMnemonic);
        console.log('Derivation Path:', result.derivationPath);
        console.log('Private Key:', result.privateKey);
        console.log('Address:', derivedAddress);
        
        // Check if this matches current wallet
        const currentPrivateKey = process.env.MAIN_WALLET_PRIVATE_KEY;
        if (currentPrivateKey && result.privateKey === currentPrivateKey) {
          console.log('✅ This mnemonic derives to your current private key!');
        } else if (currentPrivateKey) {
          const currentAddress = getAddressFromPrivateKey(currentPrivateKey);
          console.log('');
          console.log('📊 Comparison with current wallet:');
          console.log('Current Private Key:', currentPrivateKey);
          console.log('Current Address:', currentAddress);
          console.log('Match:', result.privateKey === currentPrivateKey ? '✅ YES' : '❌ NO');
        }
      } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
      }
      break;

    case 'current':
      // Show current wallet info
      const currentKey = process.env.MAIN_WALLET_PRIVATE_KEY;
      const currentMnemonic = process.env.MAIN_WALLET_MNEMONIC;
      
      console.log('📋 Current Wallet Configuration');
      console.log('================================');
      
      if (currentMnemonic) {
        const currentIndex = parseInt(process.env.MAIN_WALLET_ACCOUNT_INDEX || '0', 10);
        const current = await derivePrivateKeyFromMnemonic(currentMnemonic, currentIndex);
        const currentAddr = getAddressFromPrivateKey(current.privateKey);
        
        console.log('Method: BIP44 Mnemonic');
        console.log('Mnemonic:', currentMnemonic);
        console.log('Derivation Path:', current.derivationPath);
        console.log('Private Key:', current.privateKey);
        console.log('Address:', currentAddr);
      } else if (currentKey) {
        const currentAddr = getAddressFromPrivateKey(currentKey);
        console.log('Method: Direct Private Key');
        console.log('Private Key:', currentKey);
        console.log('Address:', currentAddr);
      } else {
        console.log('❌ No wallet configuration found');
      }
      break;

    default:
      console.log('🔧 BIP44 Wallet Setup Utility');
      console.log('==============================');
      console.log('');
      console.log('Commands:');
      console.log('  generate                           - Generate new mnemonic and wallet at index 0');
      console.log('  derive "<mnemonic>" [index]        - Derive wallet from mnemonic (default index: 0)');
      console.log('  find-target <address>              - Find mnemonic that generates target address at index 0');
      console.log('  current                            - Show current wallet configuration');
      console.log('');
      console.log('Examples:');
      console.log('  node tools/setup_bip44_wallet.js generate');
      console.log('  node tools/setup_bip44_wallet.js derive "word1 word2 ... word12" 1');
      console.log('  node tools/setup_bip44_wallet.js find-target TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx');
      console.log('  node tools/setup_bip44_wallet.js current');
      break;
  }
}

main().catch(console.error);
