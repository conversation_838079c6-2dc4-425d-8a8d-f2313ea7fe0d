const API_BASE = '';

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
  initializeTabs();
  initializeGenerateTab();
  initializeExternalTab();
  initializeVoucherTab();
  initializeBalanceTab();
  initializeNetworkTab();
  initializeTokenTab();
  initializeReserveTab();
  initializeLegacyTab();
});

// Tab Management
function initializeTabs() {
  const tabs = document.querySelectorAll('.tab');
  const tabContents = document.querySelectorAll('.tab-content');

  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // Remove active class from all tabs and contents
      tabs.forEach(t => t.classList.remove('active'));
      tabContents.forEach(tc => tc.classList.remove('active'));

      // Add active class to clicked tab
      tab.classList.add('active');
      
      // Show corresponding content
      const targetTab = tab.getAttribute('data-tab');
      document.getElementById(targetTab).classList.add('active');
    });
  });
}

// Utility Functions
function getApiKey() {
  return document.getElementById('globalApiKey').value.trim();
}

function log(message) {
  const output = document.getElementById('output');
  const timestamp = new Date().toLocaleTimeString();
  output.textContent += `[${timestamp}] ${message}\n`;
  output.scrollTop = output.scrollHeight;
}

function logSuccess(title, data) {
  log(`✅ ${title}:`);
  log(JSON.stringify(data, null, 2));
  log('─'.repeat(50));
}

function logError(title, error) {
  log(`❌ ${title}:`);
  log(typeof error === 'string' ? error : JSON.stringify(error, null, 2));
  log('─'.repeat(50));
}

async function apiCall(endpoint, method = 'GET', body = null) {
  const apiKey = getApiKey();
  if (!apiKey) {
    logError('API Error', 'API key is required');
    return null;
  }

  try {
    const options = {
      method,
      headers: {
        'x-api-key': apiKey,
        'Content-Type': 'application/json'
      }
    };
    if (body) options.body = JSON.stringify(body);

    log(`🔄 ${method} ${endpoint}...`);
    const response = await fetch(API_BASE + endpoint, options);
    const data = await response.json();
    
    if (response.ok) {
      logSuccess(`${method} ${endpoint}`, data);
      return data;
    } else {
      logError(`${method} ${endpoint}`, data);
      return null;
    }
  } catch (error) {
    logError('Network Error', error.message);
    return null;
  }
}

// Generate Off-Chain USDT Functions
function initializeGenerateTab() {
  document.getElementById('generateOffChainBtn').addEventListener('click', async () => {
    const toAddress = document.getElementById('genToAddress').value.trim();
    const amount = parseFloat(document.getElementById('genAmount').value);
    const validityMinutes = parseInt(document.getElementById('genValidity').value);

    if (!toAddress || !amount || !validityMinutes) {
      logError('Validation Error', 'Please fill all fields');
      return;
    }

    const result = await apiCall('/generate', 'POST', { toAddress, amount, validityMinutes });
    if (result) {
      log(`🎉 Successfully generated ${result.netAmount} off-chain USDT!`);
      log(`💳 Transaction ID: ${result.txID}`);
      log(`⏰ Valid until: ${result.expiresAt}`);
    }
  });
}

// External Transfer Functions
function initializeExternalTab() {
  document.getElementById('externalTransferBtn').addEventListener('click', async () => {
    const fromAddress = document.getElementById('extFromAddress').value.trim();
    const toAddress = document.getElementById('extToAddress').value.trim();
    const offChainAmount = parseFloat(document.getElementById('extAmount').value);

    if (!fromAddress || !toAddress || !offChainAmount) {
      logError('Validation Error', 'Please fill all fields');
      return;
    }

    const result = await apiCall('/external-wallet/transfer', 'POST', {
      fromAddress,
      toAddress,
      offChainAmount,
      transferType: 'direct'
    });

    if (result) {
      log(`🎉 External transfer successful!`);
      log(`💰 Real USDT sent: ${result.realUsdtAmount}`);
      log(`🔗 Blockchain TX: ${result.onChainTxID}`);
      log(`🌐 Explorer: https://nile.tronscan.org/#/transaction/${result.onChainTxID}`);
    }
  });

  document.getElementById('checkRatesBtn').addEventListener('click', async () => {
    const result = await apiCall('/external-wallet/rates');
    if (result) {
      log(`💱 Current conversion rates loaded`);
    }
  });
}

// Voucher System Functions
function initializeVoucherTab() {
  document.getElementById('createVoucherBtn').addEventListener('click', async () => {
    const fromAddress = document.getElementById('voucherFromAddress').value.trim();
    const amount = parseFloat(document.getElementById('voucherAmount').value);
    const message = document.getElementById('voucherMessage').value.trim();
    const expiryHours = parseInt(document.getElementById('voucherExpiry').value);

    if (!fromAddress || !amount || !expiryHours) {
      logError('Validation Error', 'Please fill required fields');
      return;
    }

    const result = await apiCall('/voucher/create', 'POST', {
      fromAddress,
      amount,
      message,
      expiryHours
    });

    if (result) {
      log(`🎫 Voucher created successfully!`);
      log(`🔑 Voucher Code: ${result.voucherCode}`);
      log(`💰 Value: ${result.netAmount} off-chain USDT`);
      log(`📝 Share this code with anyone to transfer the value!`);
    }
  });

  document.getElementById('redeemVoucherBtn').addEventListener('click', async () => {
    const voucherCode = document.getElementById('redeemVoucherCode').value.trim();
    const toAddress = document.getElementById('redeemToAddress').value.trim();

    if (!voucherCode || !toAddress) {
      logError('Validation Error', 'Please fill all fields');
      return;
    }

    const result = await apiCall('/voucher/redeem', 'POST', {
      voucherCode,
      toAddress
    });

    if (result) {
      log(`🎉 Voucher redeemed successfully!`);
      log(`💰 Amount: ${result.amount} off-chain USDT`);
      log(`📍 Credited to: ${result.toAddress}`);
    }
  });

  document.getElementById('checkVoucherBtn').addEventListener('click', async () => {
    const voucherCode = document.getElementById('redeemVoucherCode').value.trim();
    if (!voucherCode) {
      logError('Validation Error', 'Please enter voucher code');
      return;
    }

    const result = await apiCall(`/voucher/info/${voucherCode}`);
    if (result) {
      log(`🔍 Voucher information loaded`);
    }
  });
}

// Balance Check Functions
function initializeBalanceTab() {
  document.getElementById('checkMainBalanceBtn').addEventListener('click', async () => {
    const result = await apiCall('/balance');
    if (result) {
      log(`💰 Main wallet balance loaded`);
      log(`💳 USDT: ${result.usdt_balance}`);
      log(`⚡ TRX: ${result.trx_balance}`);
    }
  });

  document.getElementById('checkOffChainBalanceBtn').addEventListener('click', async () => {
    const address = document.getElementById('balanceCheckAddress').value.trim();
    if (!address) {
      logError('Validation Error', 'Please enter address');
      return;
    }

    const result = await apiCall(`/offchain-balance/${address}`);
    if (result) {
      log(`💳 Off-chain balance for ${address}:`);
      log(`💰 Balance: ${result.offChainBalance} USDT`);
      log(`📊 Transactions: ${result.transactionCount}`);
    }
  });

  document.getElementById('checkAllBalancesBtn').addEventListener('click', async () => {
    const result = await apiCall('/offchain-balance');
    if (result) {
      log(`📋 All off-chain balances loaded`);
      log(`👥 Total addresses: ${result.summary.totalAddresses}`);
      log(`💰 Total issued: ${result.summary.totalOffChainIssued} USDT`);
    }
  });
}

// Network Configuration Functions
function initializeNetworkTab() {
  // Load network info when tab is accessed
  loadNetworkInfo();

  document.getElementById('refreshNetworkBtn').addEventListener('click', async () => {
    await loadNetworkInfo();
  });

  document.getElementById('switchNetworkBtn').addEventListener('click', async () => {
    const selectedNetwork = document.getElementById('networkSelect').value;

    if (!selectedNetwork) {
      logError('Validation Error', 'Please select a network');
      return;
    }

    const result = await apiCall('/network/switch', 'POST', { network: selectedNetwork });
    if (result) {
      log(`🌐 Network switch initiated to ${selectedNetwork}`);
      log(`⚠️ Server restart required to apply changes`);
      log(`📝 Instructions: ${result.instructions.join(', ')}`);

      if (result.warnings) {
        result.warnings.forEach(warning => log(`⚠️ ${warning}`));
      }
    }
  });

  document.getElementById('getFaucetBtn').addEventListener('click', async () => {
    const result = await apiCall('/network/faucet');
    if (result) {
      log(`💰 Testnet faucet information loaded`);
      log(`🔗 Faucet URL: ${result.faucet}`);
      log(`📍 Main wallet: ${result.mainWalletAddress}`);

      if (result.faucet) {
        try {
          window.open(result.faucet, '_blank');
          log(`🌐 Opened faucet in new tab`);
        } catch (e) {
          log(`❌ Could not open faucet automatically`);
        }
      }
    }
  });
}

async function loadNetworkInfo() {
  const result = await apiCall('/network');
  if (result) {
    const network = result.currentNetwork;
    const wallet = result.mainWallet;

    // Update UI elements
    document.getElementById('currentNetworkName').textContent = network.name;
    document.getElementById('currentUsdtContract').textContent = network.usdtContract;
    document.getElementById('currentExplorer').textContent = network.explorer;
    document.getElementById('mainWalletAddress').textContent = wallet.address;
    document.getElementById('mainWalletUsdt').textContent = `${wallet.usdtBalance} USDT`;
    document.getElementById('mainWalletTrx').textContent = `${wallet.trxBalance} TRX`;

    // Set network selector
    document.getElementById('networkSelect').value = network.network;

    log(`🌐 Network info loaded: ${network.name}`);
    log(`💰 Main wallet balance: ${wallet.usdtBalance} USDT, ${wallet.trxBalance} TRX`);

    // Show warnings if on mainnet
    if (network.isMainnet) {
      result.warnings.forEach(warning => log(`⚠️ ${warning}`));
    }
  }
}

// Token Info Functions
function initializeTokenTab() {
  document.getElementById('getTokenInfoBtn').addEventListener('click', async () => {
    const result = await apiCall('/token-info');
    if (result) {
      log(`🪙 Token information loaded`);
      const contractAddress = result.offChainToken.contractAddress;
      document.getElementById('tokenContract').textContent = contractAddress;
      log(`📋 Contract Address: ${contractAddress}`);
      log(`🏷️ Symbol: ${result.offChainToken.symbol}`);
      log(`🔢 Decimals: ${result.offChainToken.decimals}`);
    }
  });

  document.getElementById('copyContractBtn').addEventListener('click', async () => {
    const result = await apiCall('/token-info');
    if (result) {
      const contractAddress = result.offChainToken.contractAddress;
      try {
        await navigator.clipboard.writeText(contractAddress);
        log(`📋 Contract address copied to clipboard: ${contractAddress}`);
      } catch (err) {
        log(`❌ Failed to copy to clipboard. Address: ${contractAddress}`);
      }
    }
  });

  document.getElementById('checkTokenBalanceBtn').addEventListener('click', async () => {
    const address = document.getElementById('tokenBalanceAddress').value.trim();
    if (!address) {
      logError('Validation Error', 'Please enter address');
      return;
    }

    const result = await apiCall(`/token-info/balance/${address}`);
    if (result) {
      log(`💳 Token balances for ${address}:`);
      log(`🪙 Off-Chain USDT: ${result.balances.offChainUSDT.balance} OCUSDT`);
      log(`💰 Real USDT: ${result.balances.realUSDT.balance} USDT`);
      log(`⚡ TRX: ${result.balances.TRX.balance} TRX`);
    }
  });

  document.getElementById('syncTokenBalanceBtn').addEventListener('click', async () => {
    const address = document.getElementById('tokenBalanceAddress').value.trim();
    if (!address) {
      logError('Validation Error', 'Please enter address');
      return;
    }

    const result = await apiCall(`/token-info/sync/${address}`, 'POST');
    if (result) {
      log(`🔄 Token balance synced for ${address}`);
      log(`💳 Off-Chain Balance: ${result.offChainBalance} OCUSDT`);
      log(`📱 Check your wallet for updated OCUSDT balance`);
    }
  });
}

// Reserve Status Functions
function initializeReserveTab() {
  document.getElementById('checkReserveBtn').addEventListener('click', async () => {
    const result = await apiCall('/reserve-status');
    if (result) {
      log(`📊 Reserve status loaded`);
      log(`🏦 On-chain reserve: ${result.reserve.onChainReserve}`);
      log(`📈 Utilization: ${result.reserve.utilizationRate}`);
      log(`💚 Health: ${result.health.overall}`);
    }
  });

  document.getElementById('refreshReserveBtn').addEventListener('click', async () => {
    log('🔄 Refreshing reserve status...');
    const result = await apiCall('/reserve-status');
    if (result) {
      log(`✅ Reserve status refreshed`);
    }
  });
}

// Legacy Functions
function initializeLegacyTab() {
  document.getElementById('legacyGenerateBtn').addEventListener('click', async () => {
    const toAddress = document.getElementById('legacyToAddress').value.trim();
    const amount = parseFloat(document.getElementById('legacyAmount').value);
    const validityMinutes = parseInt(document.getElementById('legacyValidity').value);

    if (!toAddress || !amount || !validityMinutes) {
      logError('Validation Error', 'Please fill all fields');
      return;
    }

    const result = await apiCall('/generate', 'POST', { toAddress, amount, validityMinutes });
    if (result) {
      log(`🎉 Legacy generate completed`);
    }
  });

  document.getElementById('legacyStatusBtn').addEventListener('click', async () => {
    const toAddress = document.getElementById('legacyToAddress').value.trim();
    if (!toAddress) {
      logError('Validation Error', 'Please enter address');
      return;
    }

    const result = await apiCall(`/status/${toAddress}`);
    if (result) {
      log(`📊 Status check completed`);
    }
  });

  document.getElementById('legacyBalanceBtn').addEventListener('click', async () => {
    const result = await apiCall('/balance');
    if (result) {
      log(`💰 Balance check completed`);
    }
  });

  document.getElementById('legacyDepositBtn').addEventListener('click', async () => {
    const toAddress = document.getElementById('legacyToAddress').value.trim();
    const amount = parseFloat(document.getElementById('legacyAmount').value);

    if (!toAddress || !amount) {
      logError('Validation Error', 'Please fill address and amount');
      return;
    }

    const result = await apiCall('/deposit', 'POST', { toAddress, amount });
    if (result) {
      log(`💳 Deposit completed`);
    }
  });
}
