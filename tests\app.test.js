import 'dotenv/config';
import request from 'supertest';
import app from '../src/index.js';

// Mock Tron and Redis modules
describe('API Tests', () => {
  let mockStore = {};
  // Mock redis client
  jest.mock('../src/config/redis', () => {
    return {
      ping: jest.fn().mockResolvedValue('PONG'),
      setEx: jest.fn(async (key, ttl, value) => { mockStore[key] = value; }),
      keys: jest.fn(async (pattern) => Object.keys(mockStore)),
      get: jest.fn(async (key) => mockStore[key])
    };
  });

  // Mock TronWeb
  jest.mock('../src/config/tron', () => {
    const fakeContract = {
      balanceOf: () => ({ call: async () => 1000000 }), // 1 USDT (6 decimals)
      transfer: () => ({ send: async () => 'tx123' })
    };
    return {
      tronWeb: {
        isConnected: async () => true,
        fromSun: (v) => v / 1e6,
        toSun: (v) => Math.floor(v * 1e6),
        contract: () => ({ at: async () => fakeContract })
      },
      mainAddress: 'TMAINADDRESS'
    };
  });

  afterEach(() => {
    mockStore = {};
    jest.resetModules();
  });

  test('Health endpoint', async () => {
    const res = await request(app).get('/health');
    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('redis', 'PONG');
    expect(res.body).toHaveProperty('tron', true);
  });

  test('Validation error on generate', async () => {
    const res = await request(app)
      .post('/generate')
      .set('x-api-key', process.env.API_KEY || 'your_api_key_here')
      .send({ toAddress: '', amount: -1, validityMinutes: 1 });
    expect(res.statusCode).toBe(400);
    expect(res.body).toHaveProperty('error');
  });

  test('Happy path generate and status', async () => {
    const apiKey = process.env.API_KEY || 'your_api_key_here';
    const payload = { toAddress: 'TADDR', amount: 0.5, validityMinutes: 10 };
    const genRes = await request(app)
      .post('/generate')
      .set('x-api-key', apiKey)
      .send(payload);
    expect(genRes.statusCode).toBe(200);
    expect(genRes.body).toHaveProperty('txID', 'tx123');
    expect(genRes.body).toHaveProperty('amount', payload.amount);
    expect(genRes.body).toHaveProperty('expiresAt');

    const statusRes = await request(app)
      .get(`/status/${payload.toAddress}`)
      .set('x-api-key', apiKey);
    expect(statusRes.statusCode).toBe(200);
    expect(Array.isArray(statusRes.body)).toBe(true);
    expect(statusRes.body.length).toBeGreaterThan(0);
    expect(statusRes.body[0]).toHaveProperty('txID', 'tx123');
  });
});