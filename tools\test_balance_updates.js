#!/usr/bin/env node
import 'dotenv/config';

const API_KEY = process.env.API_KEY;
const BASE_URL = 'http://localhost:3000';

// Helper function to make API requests
async function apiRequest(endpoint, method = 'GET', data = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'x-api-key': API_KEY,
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return { status: response.status, data: result };
  } catch (error) {
    return { status: 500, data: { error: error.message } };
  }
}

async function testBalanceUpdates() {
  console.log('🧪 Testing External Wallet Balance Updates');
  console.log('==========================================\n');
  
  const testWallet = 'TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx'; // Your wallet address
  
  // Step 1: Check initial balance
  console.log('📊 Step 1: Checking Initial Balance...');
  const initialBalanceResult = await apiRequest('/balance');
  if (initialBalanceResult.status === 200) {
    const balance = initialBalanceResult.data;
    console.log(`✅ Initial USDT Balance: ${balance.usdt_balance} USDT`);
    console.log(`✅ Initial TRX Balance: ${balance.trx_balance} TRX\n`);
  } else {
    console.log('❌ Failed to get initial balance\n');
    return;
  }
  
  // Step 2: Generate off-chain USDT
  console.log('💰 Step 2: Generating Off-Chain USDT...');
  const generateRequest = {
    toAddress: testWallet,
    amount: 100, // Generate 100 off-chain USDT
    validityMinutes: 60
  };
  
  const generateResult = await apiRequest('/generate', 'POST', generateRequest);
  if (generateResult.status === 200) {
    const result = generateResult.data;
    console.log(`✅ Generated: ${result.netAmount} off-chain USDT`);
    console.log(`✅ Transaction ID: ${result.txID}\n`);
  } else {
    console.log('❌ Failed to generate off-chain USDT:', generateResult.data.error);
    return;
  }
  
  // Step 3: Check off-chain balance
  console.log('💳 Step 3: Checking Off-Chain Balance...');
  const offChainBalanceResult = await apiRequest(`/offchain-balance/${testWallet}`);
  if (offChainBalanceResult.status === 200) {
    const balance = offChainBalanceResult.data;
    console.log(`✅ Off-Chain Balance: ${balance.offChainBalance} USDT\n`);
    
    if (balance.offChainBalance < 50) {
      console.log('❌ Insufficient off-chain balance for external transfer test');
      return;
    }
  } else {
    console.log('❌ Failed to get off-chain balance\n');
    return;
  }
  
  // Step 4: Test external transfer to same wallet (for testing)
  console.log('🌍 Step 4: Testing External Transfer (Self-Transfer for Testing)...');
  const externalTransferRequest = {
    fromAddress: testWallet,
    toAddress: testWallet, // Send to same wallet for testing
    offChainAmount: 50, // Transfer 50 off-chain USDT
    transferType: 'direct'
  };
  
  const externalResult = await apiRequest('/external-wallet/transfer', 'POST', externalTransferRequest);
  if (externalResult.status === 200) {
    const transfer = externalResult.data;
    console.log(`✅ External Transfer Completed!`);
    console.log(`✅ Off-Chain Amount: ${transfer.offChainAmount} USDT`);
    console.log(`✅ Real USDT Sent: ${transfer.realUsdtAmount} USDT`);
    console.log(`✅ Blockchain TX: ${transfer.onChainTxID}`);
    console.log(`✅ Transaction Confirmed: ${transfer.blockchain.confirmed}`);
    
    // Check balance verification
    if (transfer.balanceVerification) {
      console.log('\n📊 Balance Verification:');
      console.log(`   Balance Before: ${transfer.balanceVerification.balanceBefore}`);
      console.log(`   Balance After: ${transfer.balanceVerification.balanceAfter}`);
      console.log(`   Balance Increase: ${transfer.balanceVerification.balanceIncrease}`);
      console.log(`   Verified: ${transfer.balanceVerification.verified ? '✅ YES' : '❌ NO'}`);
      console.log(`   Note: ${transfer.balanceVerification.note}`);
    }
    console.log('');
  } else {
    console.log('❌ External transfer failed:', externalResult.data.error);
    console.log('Details:', JSON.stringify(externalResult.data, null, 2));
    return;
  }
  
  // Step 5: Check final balance to confirm update
  console.log('📈 Step 5: Checking Final Balance...');
  const finalBalanceResult = await apiRequest('/balance');
  if (finalBalanceResult.status === 200) {
    const finalBalance = finalBalanceResult.data;
    const initialUSDT = initialBalanceResult.data.usdt_balance;
    const finalUSDT = finalBalance.usdt_balance;
    const balanceChange = finalUSDT - initialUSDT;
    
    console.log(`✅ Final USDT Balance: ${finalUSDT} USDT`);
    console.log(`✅ Balance Change: ${balanceChange > 0 ? '+' : ''}${balanceChange} USDT`);
    
    if (balanceChange > 0) {
      console.log('🎉 SUCCESS: External wallet balance was updated!');
    } else {
      console.log('⚠️  WARNING: No balance increase detected');
    }
    console.log('');
  } else {
    console.log('❌ Failed to get final balance\n');
  }
  
  // Step 6: Check transaction on blockchain explorer
  if (externalResult.status === 200 && externalResult.data.onChainTxID) {
    const txID = externalResult.data.onChainTxID;
    console.log('🔍 Step 6: Blockchain Verification...');
    console.log(`Transaction ID: ${txID}`);
    console.log(`Nile Testnet Explorer: https://nile.tronscan.org/#/transaction/${txID}`);
    console.log('You can verify the transaction on the blockchain explorer above.\n');
  }
  
  console.log('📋 Test Summary:');
  console.log('================');
  console.log('✅ Off-chain USDT generation: Working');
  console.log('✅ External wallet transfer: Working');
  console.log('✅ Balance verification: Implemented');
  console.log('✅ Blockchain transaction: Recorded');
  console.log('');
  console.log('💡 If balance updates are not showing:');
  console.log('   1. Check the transaction on Nile Testnet explorer');
  console.log('   2. Ensure sufficient TRX for gas fees');
  console.log('   3. Verify network connectivity');
  console.log('   4. Wait a few minutes for network confirmation');
}

// Run the test
testBalanceUpdates().catch(console.error);
