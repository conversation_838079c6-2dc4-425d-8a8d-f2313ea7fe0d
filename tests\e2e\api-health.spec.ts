import { test, expect } from '@playwright/test';

test.describe('API Health Endpoint', () => {
  test('health check should return service status', async ({ request }) => {
    // Test the health endpoint which doesn't require authentication
    const response = await request.get('/health');
    
    expect(response.status()).toBe(200);
    
    const body = await response.json();
    expect(body).toHaveProperty('redis');
    expect(body).toHaveProperty('tron');
    expect(body.redis).toBe('PONG');
    expect(typeof body.tron).toBe('boolean');
  });

  test('health endpoint should be accessible without authentication', async ({ request }) => {
    // Verify that the health endpoint doesn't require API key
    const response = await request.get('/health');
    
    expect(response.status()).toBe(200);
    expect(response.status()).not.toBe(401);
  });

  test('health endpoint should handle service unavailability gracefully', async ({ request }) => {
    // This test verifies the error handling structure
    // In a real scenario, this would test when Redis/Tron services are down
    const response = await request.get('/health');
    
    // Should either return 200 (services up) or 500 (services down)
    expect([200, 500]).toContain(response.status());
    
    const body = await response.json();
    if (response.status() === 500) {
      expect(body).toHaveProperty('error');
      expect(body.error).toBe('Service Unavailable');
      expect(body).toHaveProperty('details');
    }
  });
});