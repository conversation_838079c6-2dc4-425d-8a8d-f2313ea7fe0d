import express from 'express';
import <PERSON><PERSON> from 'joi';
import { tronWeb } from '../config/tron.js';
import redisClient from '../config/redis.js';
import logger from '../utils/logger.js';

const router = express.Router();

const TRANSFER_FEE_PERCENT = 0.05; // 0.05% fee for off-chain transfers
const MIN_TRANSFER_AMOUNT = 0.01; // Minimum transfer amount

// Schema for off-chain transfer
const transferSchema = Joi.object({
  fromAddress: Joi.string().required(),
  toAddress: Joi.string().required(),
  amount: Joi.number().positive().min(MIN_TRANSFER_AMOUNT).required()
});

// Helper function to get available off-chain balance for an address
async function getAvailableBalance(address) {
  try {
    const keys = await redisClient.keys(`offchain:${address}:*`);
    let balance = 0;
    
    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.status === 'active') {
          balance += data.netAmount || data.amount;
        }
      }
    }
    
    return balance;
  } catch (error) {
    logger.error('Error getting available balance', { error: error.message, address });
    return 0;
  }
}

// Helper function to generate unique transfer ID
function generateTransferTxID() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `TRANSFER_${timestamp}_${random}`;
}

// Helper function to deduct balance from sender
async function deductBalance(fromAddress, amount) {
  try {
    const keys = await redisClient.keys(`offchain:${fromAddress}:*`);
    let remainingToDeduct = amount;
    const deductedFrom = [];
    
    // Sort by expiration date (deduct from expiring first)
    const records = [];
    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.status === 'active' && (data.netAmount || data.amount) > 0) {
          records.push({ key, data });
        }
      }
    }
    
    records.sort((a, b) => new Date(a.data.expiresAt) - new Date(b.data.expiresAt));
    
    // Deduct from oldest expiring records first
    for (const { key, data } of records) {
      if (remainingToDeduct <= 0) break;
      
      const availableAmount = data.netAmount || data.amount;
      const deductAmount = Math.min(availableAmount, remainingToDeduct);
      
      if (deductAmount === availableAmount) {
        // Fully consumed this record
        data.status = 'consumed';
        data.consumedAt = new Date().toISOString();
      } else {
        // Partially consumed
        data.netAmount = availableAmount - deductAmount;
        data.partiallyConsumed = true;
        data.lastDeductionAt = new Date().toISOString();
      }
      
      await redisClient.setEx(key, Math.floor((new Date(data.expiresAt) - new Date()) / 1000), JSON.stringify(data));
      
      deductedFrom.push({
        txID: data.txID,
        deductedAmount: deductAmount,
        remainingAmount: data.netAmount || 0
      });
      
      remainingToDeduct -= deductAmount;
    }
    
    return { success: remainingToDeduct === 0, deductedFrom };
  } catch (error) {
    logger.error('Error deducting balance', { error: error.message, fromAddress, amount });
    return { success: false, deductedFrom: [] };
  }
}

// POST /offchain-transfer - Transfer off-chain USDT between addresses
router.post('/', async (req, res) => {
  const { error, value } = transferSchema.validate(req.body);
  if (error) return res.status(400).json({ error: error.details[0].message });

  const { fromAddress, toAddress, amount } = value;

  try {
    // Validate TRON addresses
    if (!tronWeb.isAddress(fromAddress)) {
      return res.status(400).json({ error: 'Invalid fromAddress format' });
    }
    if (!tronWeb.isAddress(toAddress)) {
      return res.status(400).json({ error: 'Invalid toAddress format' });
    }
    
    // Prevent self-transfer
    if (fromAddress === toAddress) {
      return res.status(400).json({ error: 'Cannot transfer to the same address' });
    }

    // Check sender's available balance
    const senderBalance = await getAvailableBalance(fromAddress);
    if (senderBalance < amount) {
      return res.status(400).json({
        error: `Insufficient off-chain balance. Available: ${senderBalance} USDT, Requested: ${amount} USDT`
      });
    }

    // Calculate transfer fee
    const fee = amount * (TRANSFER_FEE_PERCENT / 100);
    const netAmount = amount - fee;

    // Deduct balance from sender
    const deductionResult = await deductBalance(fromAddress, amount);
    if (!deductionResult.success) {
      return res.status(500).json({ error: 'Failed to deduct balance from sender' });
    }

    // Generate transfer transaction ID
    const transferTxID = generateTransferTxID();
    const transferredAt = new Date().toISOString();
    const expiresAt = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(); // 1 year expiry

    // Create transfer record for recipient
    const transferRecord = {
      txID: transferTxID,
      type: 'off-chain-transfer',
      fromAddress,
      toAddress,
      amount,
      netAmount,
      fee,
      status: 'active',
      transferredAt,
      issuedAt: transferredAt, // For compatibility with balance calculation
      expiresAt,
      deductedFrom: deductionResult.deductedFrom
    };

    // Store transfer record for recipient
    const recipientKey = `offchain:${toAddress}:${transferTxID}`;
    await redisClient.setEx(recipientKey, 365 * 24 * 60 * 60, JSON.stringify(transferRecord)); // 1 year

    // Store transfer record in global tracking
    const globalKey = `global:transfer:${transferTxID}`;
    await redisClient.setEx(globalKey, 365 * 24 * 60 * 60, JSON.stringify(transferRecord));

    logger.info('Off-chain USDT transfer completed', {
      transferTxID,
      fromAddress,
      toAddress,
      amount,
      netAmount,
      fee,
      senderBalanceBefore: senderBalance,
      senderBalanceAfter: senderBalance - amount
    });

    // Get updated balances
    const senderNewBalance = await getAvailableBalance(fromAddress);
    const recipientNewBalance = await getAvailableBalance(toAddress);

    res.json({
      success: true,
      txID: transferTxID,
      type: 'off-chain-transfer',
      fromAddress,
      toAddress,
      amount,
      netAmount,
      fee: `${fee} USDT (${TRANSFER_FEE_PERCENT}%)`,
      status: 'completed',
      transferredAt,
      balances: {
        sender: {
          address: fromAddress,
          balanceBefore: senderBalance,
          balanceAfter: senderNewBalance
        },
        recipient: {
          address: toAddress,
          balanceAfter: recipientNewBalance
        }
      }
    });

  } catch (err) {
    logger.error('Off-chain transfer error', { error: err.message, stack: err.stack });
    res.status(500).json({ error: 'Failed to process off-chain transfer', details: err.message });
  }
});

export default router;
