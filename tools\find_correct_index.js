#!/usr/bin/env node
import 'dotenv/config';
import { TronWeb } from 'tronweb';
import bip39 from 'bip39';
import hdkey from 'hdkey';

const targetPrivateKey = '52dcdf5fa6d00c36d611d95aa0c11e77121ddcfaf2a7e07d6ab3edffddc202d4';
const targetAddress = 'TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx';
const mnemonic = 'spoon vibrant valley brick tiger cook million asthma village clever remember become';

async function derivePrivateKeyFromMnemonic(mnemonic, accountIndex = 0) {
  if (!bip39.validateMnemonic(mnemonic)) {
    throw new Error('Invalid mnemonic phrase');
  }
  const seed = await bip39.mnemonicToSeed(mnemonic);
  const root = hdkey.fromMasterSeed(seed);
  const derivationPath = `m/44'/195'/0'/0/${accountIndex}`;
  const addrNode = root.derive(derivationPath);
  return {
    privateKey: addrNode.privateKey.toString('hex'),
    derivationPath,
    address: TronWeb.address.fromPrivateKey(addrNode.privateKey.toString('hex'))
  };
}

async function findCorrectIndex() {
  console.log('🔍 Searching for correct derivation index...');
  console.log('Target Private Key:', targetPrivateKey);
  console.log('Target Address:', targetAddress);
  console.log('Mnemonic:', mnemonic);
  console.log('');

  // Test indices 0-20
  for (let i = 0; i <= 20; i++) {
    try {
      const result = await derivePrivateKeyFromMnemonic(mnemonic, i);
      
      const privateKeyMatch = result.privateKey === targetPrivateKey;
      const addressMatch = result.address === targetAddress;
      
      console.log(`Index ${i} (${result.derivationPath}):`);
      console.log(`  Private Key: ${result.privateKey} ${privateKeyMatch ? '✅' : '❌'}`);
      console.log(`  Address: ${result.address} ${addressMatch ? '✅' : '❌'}`);
      
      if (privateKeyMatch && addressMatch) {
        console.log('');
        console.log('🎉 FOUND CORRECT INDEX!');
        console.log('================================');
        console.log('Correct Index:', i);
        console.log('Derivation Path:', result.derivationPath);
        console.log('Private Key:', result.privateKey);
        console.log('Address:', result.address);
        return i;
      }
      console.log('');
    } catch (error) {
      console.error(`Error at index ${i}:`, error.message);
    }
  }
  
  console.log('❌ Could not find matching index in range 0-20');
  return null;
}

findCorrectIndex().catch(console.error);
