import express from 'express';
import { getNetworkConfig, getAllNetworks, switchNetwork, getExplorerUrl, getAddressExplorerUrl } from '../config/network.js';
import { tronWeb, mainAddress, networkConfig } from '../config/tron.js';
import logger from '../utils/logger.js';

const router = express.Router();

// GET /network - Get current network information
router.get('/', async (req, res) => {
  try {
    const config = getNetworkConfig();
    
    // Get main wallet balance on current network
    let mainWalletBalance = 0;
    let trxBalance = 0;
    
    try {
      const contract = await tronWeb.contract().at(config.usdtContract);
      const rawBalance = await contract.balanceOf(mainAddress).call();
      mainWalletBalance = parseFloat(rawBalance.toString()) / 1000000;
      
      const rawTrxBalance = await tronWeb.trx.getBalance(mainAddress);
      trxBalance = tronWeb.fromSun(rawTrxBalance);
    } catch (balanceError) {
      logger.warn('Could not fetch wallet balance', { error: balanceError.message });
    }
    
    res.json({
      currentNetwork: config,
      mainWallet: {
        address: mainAddress,
        usdtBalance: mainWalletBalance,
        trxBalance: parseFloat(trxBalance),
        explorerUrl: getAddressExplorerUrl(mainAddress)
      },
      capabilities: {
        generateTokens: "Generate real USDT tokens",
        externalTransfers: "Send to external wallets with fractional conversion",
        voucherSystem: "Create transferable voucher codes",
        reserveMonitoring: "Track fractional reserve ratios"
      },
      warnings: config.isMainnet ? [
        "⚠️ MAINNET MODE - Real funds will be used",
        "⚠️ All transactions cost real TRX gas fees",
        "⚠️ Generated USDT has real monetary value",
        "⚠️ Use with caution in production"
      ] : [
        "✅ Testnet mode - Safe for development",
        "✅ Free testnet tokens available",
        "✅ No real monetary value",
        "✅ Perfect for testing and learning"
      ]
    });
  } catch (error) {
    logger.error('Error getting network info', { error: error.message });
    res.status(500).json({ error: 'Failed to get network information' });
  }
});

// GET /network/all - Get all available networks
router.get('/all', async (req, res) => {
  try {
    const networks = getAllNetworks();
    const currentNetwork = getNetworkConfig().network;
    
    res.json({
      networks: networks.map(network => ({
        ...network,
        isCurrent: network.key === currentNetwork,
        contractInfo: {
          usdtContract: network.usdtContract,
          explorer: network.explorer,
          faucet: network.faucet
        }
      })),
      currentNetwork,
      switchingNote: "Network switching requires server restart to take effect"
    });
  } catch (error) {
    logger.error('Error getting all networks', { error: error.message });
    res.status(500).json({ error: 'Failed to get network list' });
  }
});

// POST /network/switch - Switch network (requires restart)
router.post('/switch', async (req, res) => {
  try {
    const { network } = req.body;
    
    if (!network) {
      return res.status(400).json({ error: 'Network parameter is required' });
    }
    
    if (!['testnet', 'mainnet'].includes(network)) {
      return res.status(400).json({ error: 'Network must be "testnet" or "mainnet"' });
    }
    
    const currentConfig = getNetworkConfig();
    if (currentConfig.network === network) {
      return res.status(400).json({ 
        error: `Already on ${network}`,
        currentNetwork: currentConfig
      });
    }
    
    // Switch network configuration
    const newConfig = switchNetwork(network);
    
    logger.info('Network switch requested', {
      from: currentConfig.network,
      to: network,
      newConfig: newConfig.name
    });
    
    res.json({
      success: true,
      message: `Network switched to ${newConfig.name}`,
      previousNetwork: currentConfig,
      newNetwork: newConfig,
      restartRequired: true,
      instructions: [
        "1. Restart the server to apply network changes",
        "2. Update your .env file NETWORK variable if needed",
        "3. Verify the new network configuration",
        "4. Check wallet balances on the new network"
      ],
      warnings: newConfig.isMainnet ? [
        "⚠️ SWITCHING TO MAINNET",
        "⚠️ Real funds will be used after restart",
        "⚠️ Ensure you have real TRX and USDT",
        "⚠️ All operations will cost real money"
      ] : [
        "✅ Switching to testnet",
        "✅ Safe for development and testing",
        "✅ Get free tokens from faucet if needed"
      ]
    });
    
  } catch (error) {
    logger.error('Error switching network', { error: error.message });
    res.status(500).json({ error: 'Failed to switch network', details: error.message });
  }
});

// GET /network/explorer/:txId - Get explorer URL for transaction
router.get('/explorer/:txId', async (req, res) => {
  try {
    const { txId } = req.params;
    const explorerUrl = getExplorerUrl(txId);
    
    res.json({
      txId,
      explorerUrl,
      network: getNetworkConfig().name,
      instructions: "Click the URL to view transaction on blockchain explorer"
    });
  } catch (error) {
    logger.error('Error getting explorer URL', { error: error.message });
    res.status(500).json({ error: 'Failed to get explorer URL' });
  }
});

// GET /network/address/:address - Get explorer URL for address
router.get('/address/:address', async (req, res) => {
  try {
    const { address } = req.params;
    
    if (!tronWeb.isAddress(address)) {
      return res.status(400).json({ error: 'Invalid TRON address format' });
    }
    
    const explorerUrl = getAddressExplorerUrl(address);
    
    res.json({
      address,
      explorerUrl,
      network: getNetworkConfig().name,
      instructions: "Click the URL to view address on blockchain explorer"
    });
  } catch (error) {
    logger.error('Error getting address explorer URL', { error: error.message });
    res.status(500).json({ error: 'Failed to get address explorer URL' });
  }
});

// GET /network/faucet - Get testnet faucet information
router.get('/faucet', async (req, res) => {
  try {
    const config = getNetworkConfig();
    
    if (config.isMainnet) {
      return res.status(400).json({
        error: 'Faucet not available on mainnet',
        message: 'You need to purchase real TRX and USDT for mainnet operations'
      });
    }
    
    res.json({
      network: config.name,
      faucet: config.faucet,
      instructions: [
        "1. Visit the faucet URL",
        "2. Enter your TRON address",
        "3. Request free TRX for gas fees",
        "4. Request free testnet USDT if available",
        "5. Wait for tokens to arrive in your wallet"
      ],
      mainWalletAddress: mainAddress,
      note: "Free testnet tokens have no real value and are for testing only"
    });
  } catch (error) {
    logger.error('Error getting faucet info', { error: error.message });
    res.status(500).json({ error: 'Failed to get faucet information' });
  }
});

export default router;
