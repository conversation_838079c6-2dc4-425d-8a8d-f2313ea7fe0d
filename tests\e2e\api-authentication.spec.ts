import { test, expect } from '@playwright/test';

test.describe('API Authentication', () => {
  const validApiKey = process.env.API_KEY || 'b6a9331327e44fdfa6c2ef8872b3c1f0a7d4c9e5f1b2a3c4d5e6f7a8b9c0d1e2';
  const invalidApiKey = 'invalid-api-key';

  test.describe('Generate endpoint authentication', () => {
    test('should reject request without API key', async ({ request }) => {
      const response = await request.post('/generate', {
        data: {
          toAddress: 'TADDR123',
          amount: 1.0,
          validityMinutes: 10
        }
      });

      expect(response.status()).toBe(401);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
      expect(body.error).toBe('Unauthorized');
    });

    test('should reject request with invalid API key', async ({ request }) => {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': invalidApi<PERSON>ey
        },
        data: {
          toAddress: 'TADDR123',
          amount: 1.0,
          validityMinutes: 10
        }
      });

      expect(response.status()).toBe(401);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
      expect(body.error).toBe('Unauthorized');
    });

    test('should accept request with valid API key via header', async ({ request }) => {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          toAddress: 'TADDR123',
          amount: 1.0,
          validityMinutes: 10
        }
      });

      // Should not be 401 (may be 400 due to validation or 200 if successful)
      expect(response.status()).not.toBe(401);
    });

    test('should accept request with valid API key via query parameter', async ({ request }) => {
      const response = await request.post(`/generate?api_key=${validApiKey}`, {
        data: {
          toAddress: 'TADDR123',
          amount: 1.0,
          validityMinutes: 10
        }
      });

      // Should not be 401 (may be 400 due to validation or 200 if successful)
      expect(response.status()).not.toBe(401);
    });
  });

  test.describe('Status endpoint authentication', () => {
    test('should reject request without API key', async ({ request }) => {
      const response = await request.get('/status/TADDR123');

      expect(response.status()).toBe(401);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
      expect(body.error).toBe('Unauthorized');
    });

    test('should reject request with invalid API key', async ({ request }) => {
      const response = await request.get('/status/TADDR123', {
        headers: {
          'x-api-key': invalidApiKey
        }
      });

      expect(response.status()).toBe(401);
      
      const body = await response.json();
      expect(body).toHaveProperty('error');
      expect(body.error).toBe('Unauthorized');
    });

    test('should accept request with valid API key', async ({ request }) => {
      const response = await request.get('/status/TADDR123', {
        headers: {
          'x-api-key': validApiKey
        }
      });

      // Should not be 401 (should be 200 with empty array or other valid response)
      expect(response.status()).not.toBe(401);
    });
  });
});