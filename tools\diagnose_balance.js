#!/usr/bin/env node
import 'dotenv/config';
import { TronWeb } from 'tronweb';

const fullNode = process.env.TRON_FULL_NODE;
const solidityNode = process.env.TRON_SOLIDITY_NODE;
const eventNode = process.env.TRON_EVENT_NODE;
const usdtContract = process.env.USDT_CONTRACT_ADDRESS;

// Initialize TronWeb
const tronWeb = new TronWeb(fullNode, solidityNode, eventNode);

async function diagnoseBalance() {
  console.log('🔍 Balance Diagnosis Tool');
  console.log('=========================');
  
  // Get wallet address from mnemonic
  const mnemonic = process.env.MAIN_WALLET_MNEMONIC;
  const accountIndex = parseInt(process.env.MAIN_WALLET_ACCOUNT_INDEX || '0', 10);
  
  let walletAddress;
  if (mnemonic) {
    // Derive address from mnemonic (simplified for diagnosis)
    const bip39 = (await import('bip39')).default;
    const hdkey = (await import('hdkey')).default;

    const seed = await bip39.mnemonicToSeed(mnemonic);
    const root = hdkey.fromMasterSeed(seed);
    const derivationPath = `m/44'/195'/0'/0/${accountIndex}`;
    const addrNode = root.derive(derivationPath);
    const privateKey = addrNode.privateKey.toString('hex');
    walletAddress = TronWeb.address.fromPrivateKey(privateKey);
  } else {
    const privateKey = process.env.MAIN_WALLET_PRIVATE_KEY;
    walletAddress = TronWeb.address.fromPrivateKey(privateKey);
  }
  
  console.log('Wallet Address:', walletAddress);
  console.log('Network:', fullNode);
  console.log('USDT Contract:', usdtContract);
  console.log('');
  
  try {
    // Check TRX balance first
    console.log('📊 Checking TRX Balance...');
    const trxBalance = await tronWeb.trx.getBalance(walletAddress);
    console.log('TRX Balance (SUN):', trxBalance);
    console.log('TRX Balance (TRX):', tronWeb.fromSun(trxBalance));
    console.log('');
    
    // Check if wallet exists
    console.log('🔍 Checking Account Info...');
    const accountInfo = await tronWeb.trx.getAccount(walletAddress);
    console.log('Account exists:', Object.keys(accountInfo).length > 0);
    console.log('Account info:', JSON.stringify(accountInfo, null, 2));
    console.log('');
    
    // Check USDT contract
    console.log('📋 Checking USDT Contract...');
    const contract = await tronWeb.contract().at(usdtContract);
    console.log('Contract loaded successfully');
    
    // Get contract info
    try {
      const name = await contract.name().call();
      const symbol = await contract.symbol().call();
      const decimals = await contract.decimals().call();
      console.log('Token Name:', name);
      console.log('Token Symbol:', symbol);
      console.log('Token Decimals:', decimals);
    } catch (e) {
      console.log('Could not get contract details:', e.message);
    }
    console.log('');
    
    // Check USDT balance
    console.log('💰 Checking USDT Balance...');
    const rawBalance = await contract.balanceOf(walletAddress).call();
    console.log('Raw Balance:', rawBalance);
    console.log('Raw Balance Type:', typeof rawBalance);
    console.log('Raw Balance toString:', rawBalance.toString());
    
    if (rawBalance._hex) {
      console.log('Raw Balance _hex:', rawBalance._hex);
      console.log('Decimal from _hex:', tronWeb.toDecimal(rawBalance._hex));
    }
    
    // Try different conversion methods
    console.log('');
    console.log('🔄 Trying different conversions...');
    
    // Method 1: Current implementation
    const balance1 = tronWeb.fromSun(rawBalance._hex ? tronWeb.toDecimal(rawBalance._hex) : rawBalance);
    console.log('Method 1 (current):', balance1);
    
    // Method 2: Direct conversion
    const balance2 = rawBalance.toString();
    console.log('Method 2 (direct string):', balance2);
    
    // Method 3: USDT has 6 decimals, not 18 like TRX
    const balance3 = parseInt(rawBalance.toString()) / 1000000; // 6 decimals
    console.log('Method 3 (6 decimals):', balance3);
    
    // Method 4: Using BigNumber
    const balance4 = rawBalance.toNumber ? rawBalance.toNumber() / 1000000 : 'N/A';
    console.log('Method 4 (BigNumber):', balance4);
    
  } catch (error) {
    console.error('❌ Error during diagnosis:', error.message);
    console.error('Full error:', error);
  }
}

diagnoseBalance().catch(console.error);
