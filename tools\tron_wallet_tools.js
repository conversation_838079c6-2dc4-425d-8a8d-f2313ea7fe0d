import { TronWeb } from 'tronweb';
import bip39 from 'bip39';
import hdkey from 'hdkey';

// Function to verify if a private key matches a TRON address
export function verifyPrivate<PERSON>ey(privateKey, address) {
  const derivedAddress = TronWeb.address.fromPrivateKey(privateKey);
  return derivedAddress === address;
}

// Function to generate a TRON wallet from a 12-word mnemonic
export async function generateWalletFromMnemonic(mnemonic, accountIndex = 0) {
  if (!bip39.validateMnemonic(mnemonic)) {
    throw new Error('Invalid mnemonic phrase');
  }
  const seed = await bip39.mnemonicToSeed(mnemonic);
  const root = hdkey.fromMasterSeed(seed);
  // TRON BIP44 derivation path: m/44'/195'/0'/0/{accountIndex}
  // Using index 1 as specified: m/44'/195'/0'/0/1
  const derivationPath = `m/44'/195'/0'/0/${accountIndex}`;
  const addrNode = root.derive(derivationPath);
  const privateKey = addrNode.privateKey.toString('hex');
  const address = TronWeb.address.fromPrivateKey(privateKey);
  return { address, privateKey, derivationPath };
}

// CLI usage example for ES modules
if (import.meta.url === process.argv[1] || import.meta.url === `file://${process.argv[1]}`) {
  const [,, cmd, ...args] = process.argv;
  if (cmd === 'verify') {
    const [privateKey, address] = args;
    const result = verifyPrivateKey(privateKey, address);
    console.log(result ? 'Match' : 'No match');
  } else if (cmd === 'generate') {
    const [mnemonic, accountIndex] = args;
    const index = accountIndex ? parseInt(accountIndex, 10) : 0; // Default to index 0
    generateWalletFromMnemonic(mnemonic, index)
      .then(({ address, privateKey, derivationPath }) => {
        console.log('Derivation Path:', derivationPath);
        console.log('Address:', address);
        console.log('Private Key:', privateKey);
      })
      .catch(e => console.error('Error:', e.message));
  } else {
    console.log('Usage:');
    console.log('  node tools/tron_wallet_tools.js verify <privateKey> <address>');
    console.log('  node tools/tron_wallet_tools.js generate "<12-word-mnemonic>" [accountIndex]');
    console.log('  Default accountIndex is 0 (m/44\'/195\'/0\'/0/0)');
  }
}
