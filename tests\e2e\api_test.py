import os
import time
import json
import requests
import redis
import pytest

# Base URL and headers
BASE_URL = f"http://localhost:{os.getenv('PORT', '3000')}"
API_KEY = os.getenv('API_KEY', 'your_key_here')
HEADERS = {'x-api-key': API_KEY}

@pytest.fixture(scope='session', autouse=True)
def wait_for_server():
    for _ in range(10):
        try:
            res = requests.get(f"{BASE_URL}/health")
            if res.status_code == 200:
                return
        except requests.ConnectionError:
            pass
        time.sleep(1)
    pytest.skip("Server not available after waiting")


def test_health_endpoint():
    res = requests.get(f"{BASE_URL}/health")
    assert res.status_code == 200
    data = res.json()
    assert data.get('redis') == 'PONG'
    assert isinstance(data.get('tron'), bool)


def test_generate_unauthorized():
    res = requests.post(f"{BASE_URL}/generate", json={})
    assert res.status_code == 401


def test_generate_bad_request():
    bad_payload = {'toAddress': 'TESTADDRESS'}
    res = requests.post(f"{BASE_URL}/generate", headers=HEADERS, json=bad_payload)
    assert res.status_code == 400


def test_status_empty_list():
    address = 'NONEXISTENT'
    res = requests.get(f"{BASE_URL}/status/{address}", headers=HEADERS)
    assert res.status_code == 200
    assert isinstance(res.json(), list)
    assert len(res.json()) == 0


def test_status_with_record():
    redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379')
    client = redis.Redis.from_url(redis_url, decode_responses=True)
    test_address = 'TESTADDR'
    test_tx = 'TESTTXID'
    record = {'txID': test_tx, 'amount': 123, 'expiresAt': '2099-01-01T00:00:00Z'}
    key = f"issue:{test_address}:{test_tx}"
    client.set(key, json.dumps(record), ex=60)

    res = requests.get(f"{BASE_URL}/status/{test_address}", headers=HEADERS)
    assert res.status_code == 200
    data = res.json()
    assert record in data
