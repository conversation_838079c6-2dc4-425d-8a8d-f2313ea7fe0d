#!/usr/bin/env node
import 'dotenv/config';

const API_KEY = process.env.API_KEY;
const BASE_URL = 'http://localhost:3000';

// Helper function to make API requests
async function apiRequest(endpoint, method = 'GET', data = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'x-api-key': API_KEY,
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return { status: response.status, data: result };
  } catch (error) {
    return { status: 500, data: { error: error.message } };
  }
}

async function testExternalWalletSystem() {
  console.log('🌐 Testing External Wallet Integration System');
  console.log('=============================================\n');
  
  // Test 1: Check conversion rates
  console.log('💱 1. Checking Conversion Rates...');
  const ratesResult = await apiRequest('/external-wallet/rates');
  if (ratesResult.status === 200) {
    const rates = ratesResult.data;
    console.log(`✅ External Transfer Rate: ${rates.externalTransfer.description}`);
    console.log(`✅ External Transfer Fee: ${rates.externalTransfer.fee}`);
    console.log(`✅ Redemption Rate: ${rates.redemption.description}`);
    console.log(`✅ Rate Advantage: ${rates.comparison.externalTransferAdvantage}\n`);
  } else {
    console.log('❌ Failed to get rates:', ratesResult.data.error);
  }
  
  // Test 2: Generate off-chain USDT first
  console.log('💰 2. Generating Off-Chain USDT for Testing...');
  const testAddress = 'TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx'; // Your wallet address
  const generateRequest = {
    toAddress: testAddress,
    amount: 5000, // Generate 5000 off-chain USDT
    validityMinutes: 60
  };
  
  const generateResult = await apiRequest('/generate', 'POST', generateRequest);
  if (generateResult.status === 200) {
    const result = generateResult.data;
    console.log(`✅ Generated: ${result.amount} off-chain USDT`);
    console.log(`✅ Net Amount: ${result.netAmount} off-chain USDT`);
    console.log(`✅ Transaction ID: ${result.txID}\n`);
  } else {
    console.log('❌ Failed to generate off-chain USDT:', generateResult.data.error);
    console.log('Note: You may already have off-chain USDT\n');
  }
  
  // Test 3: Create a transferable voucher
  console.log('🎫 3. Creating Transferable Voucher...');
  const voucherRequest = {
    fromAddress: testAddress,
    amount: 1000,
    message: "Test voucher for external sharing",
    expiryHours: 24
  };
  
  const voucherResult = await apiRequest('/voucher/create', 'POST', voucherRequest);
  if (voucherResult.status === 200) {
    const voucher = voucherResult.data;
    console.log(`✅ Voucher Created: ${voucher.voucherCode}`);
    console.log(`✅ Amount: ${voucher.netAmount} off-chain USDT`);
    console.log(`✅ Message: "${voucher.message}"`);
    console.log(`✅ Expires: ${voucher.expiresAt}`);
    console.log(`✅ Transferable: Anyone can redeem this code\n`);
    
    // Test voucher info
    console.log('🔍 3a. Checking Voucher Info...');
    const voucherInfoResult = await apiRequest(`/voucher/info/${voucher.voucherCode}`);
    if (voucherInfoResult.status === 200) {
      const info = voucherInfoResult.data;
      console.log(`✅ Voucher Status: ${info.status}`);
      console.log(`✅ Can Redeem: ${info.canRedeem}`);
      console.log(`✅ Amount: ${info.amount} off-chain USDT\n`);
    }
  } else {
    console.log('❌ Failed to create voucher:', voucherResult.data.error);
  }
  
  // Test 4: External wallet transfer (converts off-chain to real USDT)
  console.log('🌍 4. Testing External Wallet Transfer...');
  const externalAddress = 'TTestExternalWallet123456789012345678'; // Example external address
  const externalTransferRequest = {
    fromAddress: testAddress,
    toAddress: externalAddress,
    offChainAmount: 1000,
    transferType: 'direct'
  };
  
  const externalTransferResult = await apiRequest('/external-wallet/transfer', 'POST', externalTransferRequest);
  if (externalTransferResult.status === 200) {
    const transfer = externalTransferResult.data;
    console.log(`✅ External Transfer Completed!`);
    console.log(`✅ Off-Chain Amount: ${transfer.offChainAmount} USDT`);
    console.log(`✅ Real USDT Sent: ${transfer.realUsdtAmount} USDT`);
    console.log(`✅ Conversion Rate: ${transfer.conversionRate}`);
    console.log(`✅ Blockchain TX: ${transfer.onChainTxID}`);
    console.log(`✅ To External Wallet: ${transfer.toAddress}\n`);
  } else {
    console.log('❌ Failed external transfer:', externalTransferResult.data.error);
    console.log('Note: This might fail due to invalid address format or insufficient balance\n');
  }
  
  // Test 5: Redemption (fractional reserve conversion)
  console.log('🔄 5. Testing Redemption System...');
  const redemptionRequest = {
    fromAddress: testAddress,
    toAddress: testAddress, // Redeem to same address
    offChainAmount: 50000 // Minimum for redemption
  };
  
  const redemptionResult = await apiRequest('/redeem', 'POST', redemptionRequest);
  if (redemptionResult.status === 200) {
    const redemption = redemptionResult.data;
    console.log(`✅ Redemption Completed!`);
    console.log(`✅ Off-Chain Amount: ${redemption.offChainAmount} USDT`);
    console.log(`✅ Real USDT Received: ${redemption.netRealUsdt} USDT`);
    console.log(`✅ Conversion: ${redemption.conversion.description}`);
    console.log(`✅ Blockchain TX: ${redemption.onChainTxID}\n`);
  } else {
    console.log('❌ Failed redemption:', redemptionResult.data.error);
    console.log('Note: Redemption requires minimum 50,000 off-chain USDT\n');
  }
  
  // Test 6: Check updated balances
  console.log('💳 6. Checking Final Balances...');
  const finalBalanceResult = await apiRequest(`/offchain-balance/${testAddress}`);
  if (finalBalanceResult.status === 200) {
    const balance = finalBalanceResult.data;
    console.log(`✅ Final Off-Chain Balance: ${balance.offChainBalance} USDT`);
    console.log(`✅ Total Transactions: ${balance.transactionCount}\n`);
  }
  
  console.log('🎯 External Wallet Integration Summary:');
  console.log('======================================');
  console.log('✅ VOUCHER SYSTEM:');
  console.log('   • Create transferable voucher codes');
  console.log('   • Share codes with anyone (like gift cards)');
  console.log('   • Recipients redeem for off-chain USDT in your app');
  console.log('');
  console.log('✅ EXTERNAL WALLET TRANSFERS:');
  console.log('   • Convert off-chain USDT → real USDT');
  console.log('   • Send real USDT to any external TRON wallet');
  console.log('   • Better conversion rate than redemption (10% vs 0.002%)');
  console.log('');
  console.log('✅ REDEMPTION SYSTEM:');
  console.log('   • Convert large amounts at fractional reserve rate');
  console.log('   • 50,000 off-chain USDT → 1 real USDT');
  console.log('   • Maintains your 1:50,000 reserve ratio');
  console.log('');
  console.log('⚠️  IMPORTANT NOTES:');
  console.log('• External transfers use real USDT from your reserve');
  console.log('• Vouchers enable "pseudo-external" transfers via codes');
  console.log('• Recipients must use your app to redeem vouchers');
  console.log('• Real blockchain transactions occur for external transfers');
}

// Run the test
testExternalWalletSystem().catch(console.error);
