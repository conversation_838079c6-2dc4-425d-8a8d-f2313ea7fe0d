#!/usr/bin/env node
import 'dotenv/config';

const API_KEY = process.env.API_KEY;
const BASE_URL = 'http://localhost:3000';

// Helper function to make API requests
async function apiRequest(endpoint, method = 'GET', data = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'x-api-key': API_KEY,
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return { status: response.status, data: result };
  } catch (error) {
    return { status: 500, data: { error: error.message } };
  }
}

async function testNetworkSwitching() {
  console.log('🌐 Testing Network Switching & Multi-Network Support');
  console.log('===================================================\n');
  
  // Step 1: Check current network
  console.log('📊 Step 1: Checking Current Network Configuration...');
  const networkResult = await apiRequest('/network');
  if (networkResult.status === 200) {
    const network = networkResult.data.currentNetwork;
    const wallet = networkResult.data.mainWallet;
    
    console.log(`✅ Current Network: ${network.name}`);
    console.log(`✅ Network Type: ${network.network}`);
    console.log(`✅ USDT Contract: ${network.usdtContract}`);
    console.log(`✅ Explorer: ${network.explorer}`);
    console.log(`✅ Is Mainnet: ${network.isMainnet ? 'YES ⚠️' : 'NO'}`);
    console.log(`✅ Main Wallet: ${wallet.address}`);
    console.log(`✅ USDT Balance: ${wallet.usdtBalance} USDT`);
    console.log(`✅ TRX Balance: ${wallet.trxBalance} TRX\n`);
    
    if (network.isMainnet) {
      console.log('⚠️ MAINNET DETECTED - Real funds will be used!');
      networkResult.data.warnings.forEach(warning => console.log(`   ${warning}`));
      console.log('');
    }
  } else {
    console.log('❌ Failed to get network info\n');
    return;
  }
  
  // Step 2: Check all available networks
  console.log('🌍 Step 2: Checking Available Networks...');
  const allNetworksResult = await apiRequest('/network/all');
  if (allNetworksResult.status === 200) {
    const networks = allNetworksResult.data.networks;
    console.log(`✅ Available Networks: ${networks.length}`);
    
    networks.forEach(network => {
      console.log(`   ${network.isCurrent ? '👉' : '  '} ${network.name} (${network.key})`);
      console.log(`      Contract: ${network.contractInfo.usdtContract}`);
      console.log(`      Explorer: ${network.contractInfo.explorer}`);
      if (network.contractInfo.faucet) {
        console.log(`      Faucet: ${network.contractInfo.faucet}`);
      }
      console.log('');
    });
  } else {
    console.log('❌ Failed to get available networks\n');
  }
  
  // Step 3: Test token generation on current network
  console.log('💰 Step 3: Testing Token Generation on Current Network...');
  const testWallet = 'TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx';
  const generateResult = await apiRequest('/generate', 'POST', {
    toAddress: testWallet,
    amount: 100,
    validityMinutes: 60
  });
  
  if (generateResult.status === 200) {
    const result = generateResult.data;
    console.log(`✅ Token Generation Successful!`);
    console.log(`✅ Network: ${result.blockchain.network}`);
    console.log(`✅ Amount: ${result.netAmount} USDT`);
    console.log(`✅ Transaction: ${result.txID}`);
    console.log(`✅ Contract: ${result.blockchain.contractAddress}`);
    console.log(`✅ Explorer: ${result.blockchain.explorerUrl}`);
    console.log(`✅ Auto-visible in wallet: ${result.walletInfo.autoVisible ? 'YES' : 'NO'}\n`);
  } else {
    console.log('❌ Token generation failed:', generateResult.data.error);
    console.log('Note: This might be due to insufficient balance or network issues\n');
  }
  
  // Step 4: Test external transfer with fractional conversion
  console.log('🌍 Step 4: Testing External Transfer (1:50,000 Conversion)...');
  const externalWallet = 'TLsV52sRDL79HXGGm9yzwDeVXddxjHRvUb';
  const transferResult = await apiRequest('/external-wallet/transfer', 'POST', {
    fromAddress: testWallet,
    toAddress: externalWallet,
    offChainAmount: 50000,
    transferType: 'direct'
  });
  
  if (transferResult.status === 200) {
    const transfer = transferResult.data;
    console.log(`✅ External Transfer Successful!`);
    console.log(`✅ Conversion: ${transfer.conversion.description}`);
    console.log(`✅ Rate: ${transfer.conversionRate}`);
    console.log(`✅ Burned: ${transfer.conversion.burnedTokens}`);
    console.log(`✅ Sent: ${transfer.conversion.sentTokens}`);
    console.log(`✅ Blockchain TX: ${transfer.onChainTxID}\n`);
  } else {
    console.log('❌ External transfer failed:', transferResult.data.error);
    console.log('Note: This might be due to insufficient balance\n');
  }
  
  // Step 5: Test faucet information (if on testnet)
  console.log('💰 Step 5: Testing Faucet Information...');
  const faucetResult = await apiRequest('/network/faucet');
  if (faucetResult.status === 200) {
    console.log(`✅ Faucet Information Available`);
    console.log(`✅ Network: ${faucetResult.data.network}`);
    console.log(`✅ Faucet URL: ${faucetResult.data.faucet}`);
    console.log(`✅ Main Wallet: ${faucetResult.data.mainWalletAddress}\n`);
  } else {
    console.log(`ℹ️ Faucet not available: ${faucetResult.data.message || faucetResult.data.error}\n`);
  }
  
  // Step 6: Demonstrate network switching (simulation)
  console.log('🔄 Step 6: Demonstrating Network Switching...');
  const currentNetwork = networkResult.data.currentNetwork.network;
  const targetNetwork = currentNetwork === 'testnet' ? 'mainnet' : 'testnet';
  
  console.log(`Current: ${currentNetwork}, Target: ${targetNetwork}`);
  
  const switchResult = await apiRequest('/network/switch', 'POST', { network: targetNetwork });
  if (switchResult.status === 200) {
    console.log(`✅ Network Switch Initiated!`);
    console.log(`✅ From: ${switchResult.data.previousNetwork.name}`);
    console.log(`✅ To: ${switchResult.data.newNetwork.name}`);
    console.log(`✅ Restart Required: ${switchResult.data.restartRequired}`);
    console.log(`✅ Instructions:`);
    switchResult.data.instructions.forEach(instruction => {
      console.log(`   ${instruction}`);
    });
    
    if (switchResult.data.warnings) {
      console.log(`⚠️ Warnings:`);
      switchResult.data.warnings.forEach(warning => {
        console.log(`   ${warning}`);
      });
    }
    console.log('');
  } else {
    console.log(`ℹ️ Network switch: ${switchResult.data.error}\n`);
  }
  
  console.log('📋 Network Testing Summary:');
  console.log('===========================');
  console.log('✅ MULTI-NETWORK SUPPORT:');
  console.log('   • Testnet: Safe development environment');
  console.log('   • Mainnet: Production with real funds');
  console.log('   • Dynamic contract address switching');
  console.log('   • Network-specific explorer links');
  console.log('');
  console.log('✅ AUTOMATIC WALLET INTEGRATION:');
  console.log('   • Real USDT tokens sent to wallets');
  console.log('   • No custom token import needed');
  console.log('   • Appears as standard USDT balance');
  console.log('   • Works with any TRON wallet');
  console.log('');
  console.log('✅ FRACTIONAL RESERVE SYSTEM:');
  console.log('   • 1:50,000 conversion for external transfers');
  console.log('   • Maintains system sustainability');
  console.log('   • Real blockchain transactions');
  console.log('   • Proper reserve monitoring');
  console.log('');
  console.log('🎯 PRODUCTION READY FEATURES:');
  console.log('   • Mainnet support for real operations');
  console.log('   • Testnet for safe development');
  console.log('   • Network switching capability');
  console.log('   • Comprehensive error handling');
  console.log('   • Real-time balance verification');
}

// Run the test
testNetworkSwitching().catch(console.error);
