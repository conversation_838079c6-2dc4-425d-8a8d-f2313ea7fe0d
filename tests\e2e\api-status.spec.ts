import { test, expect } from '@playwright/test';

test.describe('Status API', () => {
  const validApiKey = process.env.API_KEY || 'b6a9331327e44fdfa6c2ef8872b3c1f0a7d4c9e5f1b2a3c4d5e6f7a8b9c0d1e2';

  test.describe('Status endpoint behavior', () => {
    test('should return empty array for address with no issuances', async ({ request }) => {
      const nonExistentAddress = 'TNONEXISTENT123';
      
      const response = await request.get(`/status/${nonExistentAddress}`, {
        headers: {
          'x-api-key': validApiKey
        }
      });

      expect(response.status()).toBe(200);
      
      const body = await response.json();
      expect(Array.isArray(body)).toBe(true);
      expect(body).toHaveLength(0);
    });

    test('should handle various address formats', async ({ request }) => {
      const addresses = [
        'TADDR123',
        'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', // Example TRON address format
        'TP7uRmQ1Ng1x5kYX8wEhHdL5X7Y2Z3F4G5' // Another format
      ];

      for (const address of addresses) {
        const response = await request.get(`/status/${address}`, {
          headers: {
            'x-api-key': validApiKey
          }
        });

        expect(response.status()).toBe(200);
        
        const body = await response.json();
        expect(Array.isArray(body)).toBe(true);
      }
    });

    test('should handle special characters in address', async ({ request }) => {
      // This tests URL encoding handling
      const specialAddress = 'TADDR%20TEST';
      
      const response = await request.get(`/status/${encodeURIComponent(specialAddress)}`, {
        headers: {
          'x-api-key': validApiKey
        }
      });

      // Should not crash, should return 200 with empty array
      expect(response.status()).toBe(200);
      
      const body = await response.json();
      expect(Array.isArray(body)).toBe(true);
    });
  });

  test.describe('Status response format', () => {
    test('should return array of issuances when they exist', async ({ request }) => {
      // First create an issuance
      const testAddress = 'TTESTADDR123';
      const createResponse = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          toAddress: testAddress,
          amount: 1.0,
          validityMinutes: 10
        }
      });

      // Only proceed if the generation was successful
      if (createResponse.status() === 200) {
        // Now check the status
        const statusResponse = await request.get(`/status/${testAddress}`, {
          headers: {
            'x-api-key': validApiKey
          }
        });

        expect(statusResponse.status()).toBe(200);
        
        const body = await statusResponse.json();
        expect(Array.isArray(body)).toBe(true);
        
        if (body.length > 0) {
          const issuance = body[0];
          expect(issuance).toHaveProperty('txID');
          expect(issuance).toHaveProperty('amount');
          expect(issuance).toHaveProperty('expiresAt');
          expect(typeof issuance.txID).toBe('string');
          expect(typeof issuance.amount).toBe('number');
          expect(typeof issuance.expiresAt).toBe('string');
          
          // Verify expiresAt is a valid ISO date string
          expect(() => new Date(issuance.expiresAt)).not.toThrow();
        }
      }
    });

    test('should maintain data consistency with generate endpoint', async ({ request }) => {
      const testAddress = 'TCONSISTENCY123';
      const testAmount = 2.5;
      const testValidity = 15;

      // Generate issuance
      const createResponse = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          toAddress: testAddress,
          amount: testAmount,
          validityMinutes: testValidity
        }
      });

      if (createResponse.status() === 200) {
        const createBody = await createResponse.json();

        // Check status
        const statusResponse = await request.get(`/status/${testAddress}`, {
          headers: {
            'x-api-key': validApiKey
          }
        });

        expect(statusResponse.status()).toBe(200);
        
        const statusBody = await statusResponse.json();
        expect(Array.isArray(statusBody)).toBe(true);
        
        // Find the created issuance
        const createdIssuance = statusBody.find(issuance => issuance.txID === createBody.txID);
        expect(createdIssuance).toBeDefined();
        
        if (createdIssuance) {
          expect(createdIssuance.amount).toBe(testAmount);
          expect(createdIssuance.expiresAt).toBe(createBody.expiresAt);
        }
      }
    });
  });

  test.describe('Error handling', () => {
    test('should handle service errors gracefully', async ({ request }) => {
      const response = await request.get('/status/TADDR123', {
        headers: {
          'x-api-key': validApiKey
        }
      });

      // Should either return 200 or 500, never crash
      expect([200, 500]).toContain(response.status());
      
      if (response.status() === 500) {
        const body = await response.json();
        expect(body).toHaveProperty('error');
        expect(body.error).toBe('Failed to fetch status');
        expect(body).toHaveProperty('details');
      }
    });
  });
});