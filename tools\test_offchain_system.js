#!/usr/bin/env node
import 'dotenv/config';

const API_KEY = process.env.API_KEY;
const BASE_URL = 'http://localhost:3000';

// Helper function to make API requests
async function apiRequest(endpoint, method = 'GET', data = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'x-api-key': API_KEY,
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return { status: response.status, data: result };
  } catch (error) {
    return { status: 500, data: { error: error.message } };
  }
}

async function testOffChainSystem() {
  console.log('🧪 Testing Off-Chain USDT System');
  console.log('=================================\n');
  
  // Test 1: Check reserve status
  console.log('📊 1. Checking Reserve Status...');
  const reserveStatus = await apiRequest('/reserve-status');
  if (reserveStatus.status === 200) {
    const reserve = reserveStatus.data.reserve;
    console.log(`✅ On-chain Reserve: ${reserve.onChainReserve}`);
    console.log(`✅ Max Capacity: ${reserve.maxOffChainCapacity}`);
    console.log(`✅ Current Issued: ${reserve.currentOffChainIssued}`);
    console.log(`✅ Utilization: ${reserve.utilizationRate}`);
    console.log(`✅ Health: ${reserveStatus.data.health.overall}\n`);
  } else {
    console.log('❌ Failed to get reserve status:', reserveStatus.data.error);
    return;
  }
  
  // Test 2: Generate off-chain USDT
  console.log('💰 2. Generating Off-Chain USDT...');
  const testAddress = 'TTestAddress123456789012345678901234'; // Example address
  const generateRequest = {
    toAddress: testAddress,
    amount: 1000, // Generate 1000 off-chain USDT
    validityMinutes: 60
  };
  
  const generateResult = await apiRequest('/generate', 'POST', generateRequest);
  if (generateResult.status === 200) {
    const result = generateResult.data;
    console.log(`✅ Generated: ${result.amount} USDT`);
    console.log(`✅ Net Amount: ${result.netAmount} USDT`);
    console.log(`✅ Fee: ${result.fee}`);
    console.log(`✅ Transaction ID: ${result.txID}`);
    console.log(`✅ Reserve Ratio: ${result.reserve.reserveRatio}`);
    console.log(`✅ Remaining Capacity: ${result.reserve.remainingCapacity}\n`);
  } else {
    console.log('❌ Failed to generate off-chain USDT:', generateResult.data.error);
    console.log('Note: This might fail if the test address format is invalid\n');
  }
  
  // Test 3: Check off-chain balance
  console.log('💳 3. Checking Off-Chain Balance...');
  const balanceResult = await apiRequest(`/offchain-balance/${testAddress}`);
  if (balanceResult.status === 200) {
    const balance = balanceResult.data;
    console.log(`✅ Address: ${balance.address}`);
    console.log(`✅ Off-Chain Balance: ${balance.offChainBalance} USDT`);
    console.log(`✅ Transaction Count: ${balance.transactionCount}\n`);
  } else {
    console.log('❌ Failed to get off-chain balance:', balanceResult.data.error);
    console.log('Note: This might fail if no off-chain USDT was generated\n');
  }
  
  // Test 4: Check all off-chain balances
  console.log('📋 4. Checking All Off-Chain Balances...');
  const allBalancesResult = await apiRequest('/offchain-balance');
  if (allBalancesResult.status === 200) {
    const summary = allBalancesResult.data.summary;
    console.log(`✅ Total Addresses: ${summary.totalAddresses}`);
    console.log(`✅ Total Off-Chain Issued: ${summary.totalOffChainIssued} USDT`);
    console.log(`✅ Top Holders: ${allBalancesResult.data.balances.length}\n`);
  } else {
    console.log('❌ Failed to get all balances:', allBalancesResult.data.error);
  }
  
  // Test 5: Test transfer (if we have balance)
  console.log('🔄 5. Testing Off-Chain Transfer...');
  const recipientAddress = 'TRecipient123456789012345678901234'; // Example recipient
  const transferRequest = {
    fromAddress: testAddress,
    toAddress: recipientAddress,
    amount: 100 // Transfer 100 off-chain USDT
  };
  
  const transferResult = await apiRequest('/offchain-transfer', 'POST', transferRequest);
  if (transferResult.status === 200) {
    const transfer = transferResult.data;
    console.log(`✅ Transfer Completed: ${transfer.amount} USDT`);
    console.log(`✅ Net Amount: ${transfer.netAmount} USDT`);
    console.log(`✅ Fee: ${transfer.fee}`);
    console.log(`✅ From: ${transfer.fromAddress}`);
    console.log(`✅ To: ${transfer.toAddress}`);
    console.log(`✅ Transaction ID: ${transfer.txID}\n`);
  } else {
    console.log('❌ Failed to transfer:', transferResult.data.error);
    console.log('Note: This might fail if addresses are invalid or insufficient balance\n');
  }
  
  console.log('🎯 Test Summary:');
  console.log('================');
  console.log('The off-chain USDT system allows you to:');
  console.log('• Generate off-chain USDT backed by on-chain reserves (1:50,000 ratio)');
  console.log('• Transfer off-chain USDT between addresses within your app');
  console.log('• Monitor reserve status and capacity utilization');
  console.log('• Track all off-chain balances and transactions');
  console.log('');
  console.log('⚠️  Important Notes:');
  console.log('• Off-chain USDT exists only within your application');
  console.log('• It cannot be transferred on the actual TRON blockchain');
  console.log('• Users can only spend/transfer within your app ecosystem');
  console.log('• The system maintains a fractional reserve (1 real USDT : 50,000 off-chain USDT)');
}

// Run the test
testOffChainSystem().catch(console.error);
