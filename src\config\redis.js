import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const NodeCache = require('node-cache');
const cache = new NodeCache({ stdTTL: 0, checkperiod: 60 });

export default {
  /**
   * Simulates Redis PING command.
   */
  ping: async () => 'PONG',

  /**
   * Set a key with TTL in seconds.
   */
  setEx: async (key, ttl, value) => cache.set(key, value, ttl),

  /**
   * Get all keys matching a pattern (supports trailing * only).
   */
  keys: async (pattern) => {
    const allKeys = cache.keys();
    const prefix = pattern.endsWith('*') ? pattern.slice(0, -1) : pattern;
    return allKeys.filter(k => k.startsWith(prefix));
  },

  /**
   * Get a value by key.
   */
  get: async (key) => cache.get(key),
};