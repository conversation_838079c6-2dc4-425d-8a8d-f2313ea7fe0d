import express from 'express';
import <PERSON><PERSON> from 'joi';
import { tronWeb } from '../config/tron.js';
import redisClient from '../config/redis.js';
import logger from '../utils/logger.js';

const router = express.Router();

// Helper function to get USDT balance for an address (now real on-chain USDT)
async function getOffChainBalance(address) {
  try {
    // Get real USDT balance from blockchain
    const { tronWeb } = await import('../config/tron.js');
    const USDT_CONTRACT = process.env.USDT_CONTRACT_ADDRESS;
    const contract = await tronWeb.contract().at(USDT_CONTRACT);
    const rawBalance = await contract.balanceOf(address).call();
    const balance = parseFloat(rawBalance.toString()) / 1000000;

    // Get issuance history for this address
    const keys = await redisClient.keys(`issuance:${address}:*`);
    const transactions = [];

    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        transactions.push({
          txID: data.txID,
          amount: data.netAmount || data.amount,
          type: data.type,
          issuedAt: data.issuedAt,
          expiresAt: data.expiresAt,
          status: data.status
        });
      }
    }

    return { balance, transactions };
  } catch (error) {
    logger.error('Error getting USDT balance', { error: error.message, address });
    return { balance: 0, transactions: [] };
  }
}

// Helper function to get all off-chain balances (admin function)
async function getAllOffChainBalances() {
  try {
    const keys = await redisClient.keys('offchain:*');
    const balances = {};
    let totalIssued = 0;
    
    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.status === 'active') {
          const address = data.toAddress;
          if (!balances[address]) {
            balances[address] = {
              address,
              balance: 0,
              transactionCount: 0,
              lastActivity: null
            };
          }
          balances[address].balance += data.netAmount || data.amount;
          balances[address].transactionCount += 1;
          balances[address].lastActivity = data.issuedAt;
          totalIssued += data.netAmount || data.amount;
        }
      }
    }
    
    return { balances: Object.values(balances), totalIssued };
  } catch (error) {
    logger.error('Error getting all off-chain balances', { error: error.message });
    return { balances: [], totalIssued: 0 };
  }
}

// GET /offchain-balance/:address - Get off-chain balance for specific address
router.get('/:address', async (req, res) => {
  try {
    const { address } = req.params;
    
    // Validate TRON address format
    if (!tronWeb.isAddress(address)) {
      return res.status(400).json({ error: 'Invalid TRON address format' });
    }
    
    const { balance, transactions } = await getOffChainBalance(address);
    
    res.json({
      address,
      usdtBalance: balance,
      offChainBalance: balance, // For compatibility
      transactionCount: transactions.length,
      transactions: transactions.sort((a, b) => new Date(b.issuedAt) - new Date(a.issuedAt)),
      tokenInfo: {
        symbol: "USDT",
        name: "Tether USD",
        contractAddress: process.env.USDT_CONTRACT_ADDRESS,
        decimals: 6,
        network: "TRON Nile Testnet"
      },
      lastUpdated: new Date().toISOString(),
      note: "Real USDT tokens on TRON blockchain - visible in any TRON wallet"
    });
    
  } catch (err) {
    logger.error('Off-chain balance fetch error', { error: err.message });
    res.status(500).json({ error: 'Failed to fetch off-chain balance', details: err.message });
  }
});

// GET /offchain-balance - Get all off-chain balances (admin function)
router.get('/', async (req, res) => {
  try {
    const { balances, totalIssued } = await getAllOffChainBalances();
    
    res.json({
      summary: {
        totalAddresses: balances.length,
        totalOffChainIssued: totalIssued,
        lastUpdated: new Date().toISOString()
      },
      balances: balances.sort((a, b) => b.balance - a.balance) // Sort by balance descending
    });
    
  } catch (err) {
    logger.error('All off-chain balances fetch error', { error: err.message });
    res.status(500).json({ error: 'Failed to fetch all off-chain balances', details: err.message });
  }
});

export default router;
