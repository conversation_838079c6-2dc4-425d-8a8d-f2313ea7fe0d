import express from 'express';
import Jo<PERSON> from 'joi';
import { tronWeb } from '../config/tron.js';
import redisClient from '../config/redis.js';
import logger from '../utils/logger.js';
import crypto from 'crypto';

const router = express.Router();

const VOUCHER_FEE_PERCENT = 0.1; // 0.1% fee for creating vouchers
const MIN_VOUCHER_AMOUNT = 1; // Minimum 1 off-chain USDT for voucher
const VOUCHER_EXPIRY_HOURS = 24; // Vouchers expire in 24 hours

// Schema for voucher creation
const createVoucherSchema = Joi.object({
  fromAddress: Joi.string().required(),
  amount: Joi.number().positive().min(MIN_VOUCHER_AMOUNT).required(),
  message: Joi.string().max(200).optional(),
  expiryHours: Joi.number().integer().min(1).max(168).default(VOUCHER_EXPIRY_HOURS) // Max 1 week
});

// Schema for voucher redemption
const redeemVoucherSchema = Joi.object({
  voucherCode: Joi.string().required(),
  toAddress: Joi.string().required()
});

// Helper function to get available off-chain balance
async function getAvailableBalance(address) {
  try {
    const keys = await redisClient.keys(`offchain:${address}:*`);
    let balance = 0;
    
    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.status === 'active') {
          balance += data.netAmount || data.amount;
        }
      }
    }
    
    return balance;
  } catch (error) {
    logger.error('Error getting available balance', { error: error.message, address });
    return 0;
  }
}

// Helper function to deduct off-chain balance for voucher creation
async function deductOffChainBalance(fromAddress, amount) {
  try {
    const keys = await redisClient.keys(`offchain:${fromAddress}:*`);
    let remainingToDeduct = amount;
    const deductedFrom = [];
    
    const records = [];
    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.status === 'active' && (data.netAmount || data.amount) > 0) {
          records.push({ key, data });
        }
      }
    }
    
    records.sort((a, b) => new Date(a.data.expiresAt) - new Date(b.data.expiresAt));
    
    for (const { key, data } of records) {
      if (remainingToDeduct <= 0) break;
      
      const availableAmount = data.netAmount || data.amount;
      const deductAmount = Math.min(availableAmount, remainingToDeduct);
      
      if (deductAmount === availableAmount) {
        data.status = 'voucher-locked';
        data.voucherLockedAt = new Date().toISOString();
      } else {
        data.netAmount = availableAmount - deductAmount;
        data.partiallyVoucherLocked = true;
        data.lastVoucherLockAt = new Date().toISOString();
      }
      
      await redisClient.setEx(key, Math.floor((new Date(data.expiresAt) - new Date()) / 1000), JSON.stringify(data));
      
      deductedFrom.push({
        txID: data.txID,
        deductedAmount: deductAmount,
        remainingAmount: data.netAmount || 0
      });
      
      remainingToDeduct -= deductAmount;
    }
    
    return { success: remainingToDeduct === 0, deductedFrom };
  } catch (error) {
    logger.error('Error deducting off-chain balance for voucher', { error: error.message, fromAddress, amount });
    return { success: false, deductedFrom: [] };
  }
}

// Helper function to generate secure voucher code
function generateVoucherCode() {
  const timestamp = Date.now().toString(36);
  const random = crypto.randomBytes(16).toString('hex');
  return `VOUCHER_${timestamp}_${random}`.toUpperCase();
}

// POST /voucher/create - Create a transferable voucher
router.post('/create', async (req, res) => {
  const { error, value } = createVoucherSchema.validate(req.body);
  if (error) return res.status(400).json({ error: error.details[0].message });

  const { fromAddress, amount, message, expiryHours } = value;

  try {
    // Validate TRON address
    if (!tronWeb.isAddress(fromAddress)) {
      return res.status(400).json({ error: 'Invalid fromAddress format' });
    }

    // Check sender's available off-chain balance
    const senderBalance = await getAvailableBalance(fromAddress);
    if (senderBalance < amount) {
      return res.status(400).json({
        error: `Insufficient off-chain balance. Available: ${senderBalance} USDT, Requested: ${amount} USDT`
      });
    }

    // Calculate voucher fee
    const fee = amount * (VOUCHER_FEE_PERCENT / 100);
    const netAmount = amount - fee;

    // Deduct off-chain balance from sender
    const deductionResult = await deductOffChainBalance(fromAddress, amount);
    if (!deductionResult.success) {
      return res.status(500).json({ error: 'Failed to deduct off-chain balance for voucher' });
    }

    // Generate voucher code and details
    const voucherCode = generateVoucherCode();
    const createdAt = new Date().toISOString();
    const expiresAt = new Date(Date.now() + expiryHours * 60 * 60 * 1000).toISOString();

    const voucherRecord = {
      voucherCode,
      type: 'voucher',
      fromAddress,
      amount,
      netAmount,
      fee,
      message: message || '',
      status: 'active',
      createdAt,
      expiresAt,
      expiryHours,
      deductedFrom: deductionResult.deductedFrom,
      redeemedBy: null,
      redeemedAt: null
    };

    // Store voucher record
    const voucherKey = `voucher:${voucherCode}`;
    await redisClient.setEx(voucherKey, expiryHours * 60 * 60, JSON.stringify(voucherRecord));

    // Store in creator's voucher history
    const historyKey = `voucher-history:${fromAddress}:${voucherCode}`;
    await redisClient.setEx(historyKey, expiryHours * 60 * 60, JSON.stringify(voucherRecord));

    logger.info('Voucher created', {
      voucherCode,
      fromAddress,
      amount,
      netAmount,
      expiresAt
    });

    res.json({
      success: true,
      voucherCode,
      type: 'voucher',
      fromAddress,
      amount,
      netAmount,
      fee: `${fee} USDT (${VOUCHER_FEE_PERCENT}%)`,
      message: message || '',
      status: 'active',
      createdAt,
      expiresAt,
      instructions: {
        sharing: "Share this voucher code with anyone to transfer off-chain USDT",
        redemption: `POST /voucher/redeem with voucherCode and toAddress`,
        expiry: `Voucher expires in ${expiryHours} hours`,
        transferable: "This voucher can be redeemed by any valid TRON address"
      }
    });

  } catch (err) {
    logger.error('Voucher creation error', { error: err.message, stack: err.stack });
    res.status(500).json({ error: 'Failed to create voucher', details: err.message });
  }
});

// POST /voucher/redeem - Redeem a voucher
router.post('/redeem', async (req, res) => {
  const { error, value } = redeemVoucherSchema.validate(req.body);
  if (error) return res.status(400).json({ error: error.details[0].message });

  const { voucherCode, toAddress } = value;

  try {
    // Validate TRON address
    if (!tronWeb.isAddress(toAddress)) {
      return res.status(400).json({ error: 'Invalid toAddress format' });
    }

    // Get voucher record
    const voucherKey = `voucher:${voucherCode}`;
    const voucherRecord = await redisClient.get(voucherKey);

    if (!voucherRecord) {
      return res.status(404).json({ error: 'Voucher not found or expired' });
    }

    const voucher = JSON.parse(voucherRecord);

    if (voucher.status !== 'active') {
      return res.status(400).json({ error: 'Voucher has already been redeemed' });
    }

    // Check if voucher has expired
    if (new Date() > new Date(voucher.expiresAt)) {
      return res.status(400).json({ error: 'Voucher has expired' });
    }

    // Generate redemption transaction ID
    const redemptionTxID = `VOUCHER_REDEEM_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    const redeemedAt = new Date().toISOString();

    // Create off-chain balance record for recipient
    const transferRecord = {
      txID: redemptionTxID,
      type: 'voucher-redemption',
      fromAddress: voucher.fromAddress,
      toAddress,
      amount: voucher.netAmount,
      netAmount: voucher.netAmount,
      fee: 0, // No additional fee for redemption
      status: 'active',
      transferredAt: redeemedAt,
      issuedAt: redeemedAt,
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year
      voucherCode,
      originalVoucherAmount: voucher.amount,
      message: voucher.message
    };

    // Store transfer record for recipient
    const recipientKey = `offchain:${toAddress}:${redemptionTxID}`;
    await redisClient.setEx(recipientKey, 365 * 24 * 60 * 60, JSON.stringify(transferRecord));

    // Update voucher status
    voucher.status = 'redeemed';
    voucher.redeemedBy = toAddress;
    voucher.redeemedAt = redeemedAt;
    voucher.redemptionTxID = redemptionTxID;

    // Update voucher record
    await redisClient.setEx(voucherKey, Math.floor((new Date(voucher.expiresAt) - new Date()) / 1000), JSON.stringify(voucher));

    logger.info('Voucher redeemed', {
      voucherCode,
      redemptionTxID,
      fromAddress: voucher.fromAddress,
      toAddress,
      amount: voucher.netAmount
    });

    res.json({
      success: true,
      redemptionTxID,
      voucherCode,
      type: 'voucher-redemption',
      fromAddress: voucher.fromAddress,
      toAddress,
      amount: voucher.netAmount,
      message: voucher.message,
      status: 'completed',
      redeemedAt,
      originalVoucher: {
        createdAt: voucher.createdAt,
        originalAmount: voucher.amount,
        fee: voucher.fee
      },
      note: "Off-chain USDT has been added to your balance and can be used within the app"
    });

  } catch (err) {
    logger.error('Voucher redemption error', { error: err.message, stack: err.stack });
    res.status(500).json({ error: 'Failed to redeem voucher', details: err.message });
  }
});

// GET /voucher/info/:code - Get voucher information without redeeming
router.get('/info/:code', async (req, res) => {
  try {
    const { code } = req.params;
    const voucherKey = `voucher:${code}`;
    const voucherRecord = await redisClient.get(voucherKey);

    if (!voucherRecord) {
      return res.status(404).json({ error: 'Voucher not found or expired' });
    }

    const voucher = JSON.parse(voucherRecord);
    const isExpired = new Date() > new Date(voucher.expiresAt);

    res.json({
      voucherCode: voucher.voucherCode,
      amount: voucher.netAmount,
      message: voucher.message,
      status: isExpired ? 'expired' : voucher.status,
      createdAt: voucher.createdAt,
      expiresAt: voucher.expiresAt,
      isExpired,
      canRedeem: voucher.status === 'active' && !isExpired,
      redeemedBy: voucher.redeemedBy,
      redeemedAt: voucher.redeemedAt
    });

  } catch (err) {
    logger.error('Voucher info error', { error: err.message });
    res.status(500).json({ error: 'Failed to get voucher info', details: err.message });
  }
});

export default router;
