#!/usr/bin/env node
import 'dotenv/config';

const API_KEY = process.env.API_KEY;
const BASE_URL = 'http://localhost:3000';

// Helper function to make API requests
async function apiRequest(endpoint, method = 'GET', data = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'x-api-key': API_KEY,
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return { status: response.status, data: result };
  } catch (error) {
    return { status: 500, data: { error: error.message } };
  }
}

async function demonstrateWorkingMethods() {
  console.log('✅ WORKING Methods to Increase Wallet Balances');
  console.log('==============================================');
  console.log('Educational demonstration of off-chain USDT system\n');
  
  const mainWallet = 'TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx'; // Your main wallet
  const externalWallet = 'TLsV52sRDL79HXGGm9yzwDeVXddxjHRvUb'; // Valid TRON address format
  
  console.log('🎯 METHOD 1: External Transfer (Real USDT to Blockchain)');
  console.log('========================================================');
  
  // Step 1: Generate off-chain USDT
  console.log('💰 Generating Off-Chain USDT...');
  const generateResult = await apiRequest('/generate', 'POST', {
    toAddress: mainWallet,
    amount: 100,
    validityMinutes: 60
  });
  
  if (generateResult.status === 200) {
    console.log(`✅ Generated: ${generateResult.data.netAmount} off-chain USDT`);
  }
  
  // Step 2: Send to external wallet as real USDT
  console.log('\n🌍 Sending to External Wallet...');
  const transferResult = await apiRequest('/external-wallet/transfer', 'POST', {
    fromAddress: mainWallet,
    toAddress: externalWallet,
    offChainAmount: 50,
    transferType: 'direct'
  });
  
  if (transferResult.status === 200) {
    const transfer = transferResult.data;
    console.log(`✅ SUCCESS: Real USDT sent to external wallet!`);
    console.log(`✅ Amount: ${transfer.realUsdtAmount} USDT`);
    console.log(`✅ Transaction: ${transfer.onChainTxID}`);
    console.log(`✅ Explorer: https://nile.tronscan.org/#/transaction/${transfer.onChainTxID}`);
    console.log(`✅ External wallet balance increased by real USDT!`);
  } else {
    console.log(`❌ Transfer failed: ${transferResult.data.error}`);
  }
  
  console.log('\n🎫 METHOD 2: Voucher System (Shareable Codes)');
  console.log('==============================================');
  
  // Step 3: Create voucher
  console.log('📝 Creating Transferable Voucher...');
  const voucherResult = await apiRequest('/voucher/create', 'POST', {
    fromAddress: mainWallet,
    amount: 25,
    message: "Gift USDT - redeem this code!",
    expiryHours: 24
  });
  
  if (voucherResult.status === 200) {
    const voucher = voucherResult.data;
    console.log(`✅ Voucher Created: ${voucher.voucherCode}`);
    console.log(`✅ Value: ${voucher.netAmount} off-chain USDT`);
    console.log(`✅ Anyone can redeem this code for off-chain USDT`);
    
    // Step 4: Redeem voucher to external wallet
    console.log('\n🔄 Redeeming Voucher to External Wallet...');
    const redeemResult = await apiRequest('/voucher/redeem', 'POST', {
      voucherCode: voucher.voucherCode,
      toAddress: externalWallet
    });
    
    if (redeemResult.status === 200) {
      console.log(`✅ SUCCESS: Voucher redeemed!`);
      console.log(`✅ External wallet now has ${redeemResult.data.amount} off-chain USDT`);
      console.log(`✅ Can be used within app or sent to other external wallets`);
    }
  }
  
  console.log('\n💰 METHOD 3: Direct Generation (Instant Balance)');
  console.log('=================================================');
  
  // Step 5: Generate directly in external wallet
  console.log('⚡ Generating Off-Chain USDT Directly in External Wallet...');
  const directResult = await apiRequest('/generate', 'POST', {
    toAddress: externalWallet,
    amount: 75,
    validityMinutes: 120
  });
  
  if (directResult.status === 200) {
    console.log(`✅ SUCCESS: Direct generation completed!`);
    console.log(`✅ Generated: ${directResult.data.netAmount} off-chain USDT`);
    console.log(`✅ External wallet balance increased instantly!`);
  }
  
  console.log('\n📊 Final Balance Check...');
  const balanceResult = await apiRequest(`/offchain-balance/${externalWallet}`);
  if (balanceResult.status === 200) {
    console.log(`✅ External Wallet Total Balance: ${balanceResult.data.offChainBalance} off-chain USDT`);
    console.log(`✅ Transaction Count: ${balanceResult.data.transactionCount}`);
  }
  
  console.log('\n🎯 SUMMARY - How to Increase External Wallet Balances:');
  console.log('======================================================');
  console.log('');
  console.log('✅ METHOD 1 - REAL USDT TRANSFER:');
  console.log('   • Sends actual USDT to external wallet via blockchain');
  console.log('   • 1:1 conversion (educational demo)');
  console.log('   • Increases real USDT balance that shows in any wallet app');
  console.log('   • Consumes gas fees but creates real blockchain transaction');
  console.log('   • Perfect for: Payments, withdrawals, real value transfer');
  console.log('');
  console.log('✅ METHOD 2 - VOUCHER CODES:');
  console.log('   • Create shareable codes worth off-chain USDT');
  console.log('   • Share via SMS, email, QR codes, social media');
  console.log('   • Recipients redeem for off-chain USDT in their wallet');
  console.log('   • No blockchain fees, instant redemption');
  console.log('   • Perfect for: Gifts, promotions, referral rewards');
  console.log('');
  console.log('✅ METHOD 3 - DIRECT GENERATION:');
  console.log('   • Generate off-chain USDT directly in any wallet address');
  console.log('   • Instant balance increase');
  console.log('   • Backed by fractional reserves (1:50,000 ratio)');
  console.log('   • Can later be sent as real USDT to external wallets');
  console.log('   • Perfect for: App rewards, loyalty points, gaming tokens');
  console.log('');
  console.log('🔄 CONVERSION FLOW:');
  console.log('   Off-Chain USDT → Real USDT (via external transfer)');
  console.log('   Off-Chain USDT → Voucher Codes (shareable)');
  console.log('   Off-Chain USDT → Internal Transfers (between app users)');
  console.log('');
  console.log('💡 KEY INSIGHT:');
  console.log('   Off-chain USDT can be "materialized" as real USDT during validity period');
  console.log('   This demonstrates fractional reserve banking with blockchain integration');
  console.log('   Educational system shows how digital tokens can have real-world value');
}

// Run the demonstration
demonstrateWorkingMethods().catch(console.error);
