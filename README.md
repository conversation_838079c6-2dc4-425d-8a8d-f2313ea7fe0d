# USDT-TRC20 Issuance Service

Off-chain issuance service for USDT on the TRON network. Built with Node.js, Express, TronWeb, Redis, and Docker.

## Features

- Generate signed USDT-TRC20 transfers without exposing the main private key
- Time-limited issuance records stored in Redis with custom TTL
- API key authentication and rate limiting
- Health check endpoint for TronGrid and Redis
- Structured logging with request IDs
- OpenAPI (Swagger) documentation
- Dockerized app and Redis via Docker Compose

## Prerequisites

- Node.js v14 or higher
- Redis server (if running locally)
- Docker & Docker Compose (optional, for containerized deployment)

## Installation

1. Clone the repo:
   ```bash
   git clone <repo-url> usdt-trc20-issuance-service
   cd usdt-trc20-issuance-service
   ```
2. Install dependencies:
   ```bash
   npm install
   ```
3. Copy and configure environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your private key, Tron nodes, Redis URL, API key, etc.
   ```

## Environment Variables

- `MAIN_WALLET_PRIVATE_KEY`: Private key of the main wallet
- `TRON_FULL_NODE`, `TRON_SOLIDITY_NODE`, `TRON_EVENT_NODE`: TronGrid endpoints
- `USDT_CONTRACT_ADDRESS`: USDT-TRC20 contract address
- `REDIS_URL`: Redis connection URI (e.g., `redis://localhost:6379`)
- `PORT`: HTTP server port (default: 3000)
- `API_KEY`: Simple API key for all requests

## Running Locally

Start the server in development mode (with hot reload):
```bash
npm run dev
```

Start in production mode:
```bash
npm start
```

Swagger UI is available at http://localhost:3000/docs

## API Endpoints

- `GET /health` — checks Redis and TronGrid connectivity
- `POST /generate` — create a signed USDT transfer request
  - Body: `{ toAddress: string, amount: number, validityMinutes: number }`
  - Returns: `{ txID, amount, expiresAt }`
- `GET /status/{address}` — list active issuance records for a given address

All endpoints require an `x-api-key` header (or `?api_key=` query param).

## Testing

Run the test suite:
```bash
npm test
```

## Docker

1. Build and start services via Docker Compose:
   ```bash
   docker-compose up --build
   ```
2. The API will be available at http://localhost:3000
3. Redis is exposed on port 6379

## License

MIT License
