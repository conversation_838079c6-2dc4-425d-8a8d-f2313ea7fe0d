<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Off-Chain USDT System - Educational Demo</title>
  <style>
    * { box-sizing: border-box; }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: white;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 2.5em;
      font-weight: 300;
    }
    .header p {
      margin: 10px 0 0 0;
      opacity: 0.9;
      font-size: 1.1em;
    }
    .tabs {
      display: flex;
      background: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
    }
    .tab {
      flex: 1;
      padding: 15px 20px;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #6c757d;
      transition: all 0.3s ease;
      border-bottom: 3px solid transparent;
    }
    .tab:hover {
      background: #e9ecef;
      color: #495057;
    }
    .tab.active {
      color: #007bff;
      border-bottom-color: #007bff;
      background: white;
    }
    .tab-content {
      display: none;
      padding: 30px;
    }
    .tab-content.active {
      display: block;
    }
    .form-group {
      margin-bottom: 20px;
    }
    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #495057;
    }
    .form-group input, .form-group textarea, .form-group select {
      width: 100%;
      padding: 12px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }
    .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
      outline: none;
      border-color: #007bff;
    }
    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    .btn-primary {
      background: #007bff;
      color: white;
    }
    .btn-primary:hover {
      background: #0056b3;
      transform: translateY(-2px);
    }
    .btn-success {
      background: #28a745;
      color: white;
    }
    .btn-success:hover {
      background: #1e7e34;
      transform: translateY(-2px);
    }
    .btn-warning {
      background: #ffc107;
      color: #212529;
    }
    .btn-warning:hover {
      background: #e0a800;
      transform: translateY(-2px);
    }
    .btn-info {
      background: #17a2b8;
      color: white;
    }
    .btn-info:hover {
      background: #117a8b;
      transform: translateY(-2px);
    }
    .output {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
      max-height: 400px;
      overflow: auto;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      line-height: 1.4;
    }
    .row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }
    .col {
      flex: 1;
    }
    .alert {
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
    }
    .alert-info {
      background: #d1ecf1;
      border: 1px solid #bee5eb;
      color: #0c5460;
    }
    .alert-success {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
    }
    .feature-card {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
    }
    .feature-card h3 {
      margin-top: 0;
      color: #495057;
    }
    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      margin-left: 10px;
    }
    .status-working {
      background: #d4edda;
      color: #155724;
    }
    .status-demo {
      background: #fff3cd;
      color: #856404;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🏦 Off-Chain USDT System</h1>
      <p>Educational Demo - Fractional Reserve Banking with 1:1 External Transfers</p>
    </div>

    <div class="tabs">
      <button class="tab active" data-tab="generate">💰 Generate USDT</button>
      <button class="tab" data-tab="external">🌍 External Transfer</button>
      <button class="tab" data-tab="voucher">🎫 Voucher System</button>
      <button class="tab" data-tab="balance">💳 Check Balances</button>
      <button class="tab" data-tab="network">🌐 Network Config</button>
      <button class="tab" data-tab="reserve">📊 Reserve Status</button>
      <button class="tab" data-tab="legacy">🔧 Legacy Functions</button>
    </div>

    <!-- Global API Key -->
    <div style="padding: 20px; background: #f8f9fa; border-bottom: 1px solid #dee2e6;">
      <div class="form-group">
        <label>🔑 API Key (Required for all operations):</label>
        <input id="globalApiKey" type="text" placeholder="Enter your API key" value="b6a9331327e44fdfa6c2ef8872b3c1f0a7d4c9e5f1b2a3c4d5e6f7a8b9c0d1e2" />
      </div>
    </div>

    <!-- Generate Off-Chain USDT Tab -->
    <div id="generate" class="tab-content active">
      <div class="alert alert-success">
        <strong>💰 Generate Real USDT Tokens</strong><br>
        Generate real USDT tokens on TRON Nile testnet that appear automatically in wallets. Backed by fractional reserves (1:50,000 ratio).
      </div>

      <div class="row">
        <div class="col">
          <div class="form-group">
            <label>Recipient Address:</label>
            <input id="genToAddress" type="text" placeholder="TRON address (e.g., TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx)" />
          </div>
          <div class="form-group">
            <label>Amount (USDT):</label>
            <input id="genAmount" type="number" step="0.01" placeholder="e.g., 1000" />
          </div>
          <div class="form-group">
            <label>Validity Period (minutes):</label>
            <input id="genValidity" type="number" placeholder="e.g., 60" value="60" />
          </div>
          <button class="btn btn-primary" id="generateOffChainBtn">💰 Generate Real USDT</button>
        </div>
        <div class="col">
          <div class="feature-card">
            <h3>How it works:</h3>
            <ul>
              <li>✅ Sends real USDT tokens to your wallet</li>
              <li>✅ Appears automatically as USDT balance</li>
              <li>✅ No custom token import needed</li>
              <li>✅ Backed by 1:50,000 fractional reserves</li>
              <li>✅ Works with any TRON wallet</li>
              <li>✅ Small issuance fee (0.1%)</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- External Transfer Tab -->
    <div id="external" class="tab-content">
      <div class="alert alert-success">
        <strong>🌍 External Wallet Transfer</strong><span class="status-badge status-working">✅ WORKING</span><br>
        Convert your USDT to reserve USDT and send to external wallets (1:50,000 fractional reserve conversion).
      </div>

      <div class="row">
        <div class="col">
          <div class="form-group">
            <label>From Address (Your Wallet):</label>
            <input id="extFromAddress" type="text" placeholder="Your TRON address with off-chain USDT" />
          </div>
          <div class="form-group">
            <label>To Address (External Wallet):</label>
            <input id="extToAddress" type="text" placeholder="External TRON address" />
          </div>
          <div class="form-group">
            <label>Off-Chain Amount to Send:</label>
            <input id="extAmount" type="number" step="0.01" placeholder="e.g., 100" />
          </div>
          <button class="btn btn-success" id="externalTransferBtn">🌍 Send to External Wallet</button>
          <button class="btn btn-info" id="checkRatesBtn">💱 Check Conversion Rates</button>
        </div>
        <div class="col">
          <div class="feature-card">
            <h3>External Transfer Features:</h3>
            <ul>
              <li>✅ 1:50,000 fractional reserve conversion</li>
              <li>✅ Real USDT sent via TRON blockchain</li>
              <li>✅ Balance verification included</li>
              <li>✅ Transaction appears on blockchain explorer</li>
              <li>⚠️ Consumes real USDT from reserves</li>
              <li>⚠️ Gas fees apply</li>
              <li>💡 50,000 off-chain USDT = 1 real USDT</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Voucher System Tab -->
    <div id="voucher" class="tab-content">
      <div class="alert alert-info">
        <strong>🎫 Voucher System</strong><span class="status-badge status-working">✅ WORKING</span><br>
        Create transferable voucher codes that can be shared and redeemed for off-chain USDT.
      </div>

      <div class="row">
        <div class="col">
          <h3>Create Voucher</h3>
          <div class="form-group">
            <label>From Address:</label>
            <input id="voucherFromAddress" type="text" placeholder="Your TRON address" />
          </div>
          <div class="form-group">
            <label>Amount:</label>
            <input id="voucherAmount" type="number" step="0.01" placeholder="e.g., 50" />
          </div>
          <div class="form-group">
            <label>Message (optional):</label>
            <input id="voucherMessage" type="text" placeholder="e.g., Gift for you!" />
          </div>
          <div class="form-group">
            <label>Expiry Hours:</label>
            <input id="voucherExpiry" type="number" placeholder="e.g., 24" value="24" />
          </div>
          <button class="btn btn-primary" id="createVoucherBtn">🎫 Create Voucher</button>
        </div>
        <div class="col">
          <h3>Redeem Voucher</h3>
          <div class="form-group">
            <label>Voucher Code:</label>
            <input id="redeemVoucherCode" type="text" placeholder="VOUCHER_ABC123..." />
          </div>
          <div class="form-group">
            <label>Redeem To Address:</label>
            <input id="redeemToAddress" type="text" placeholder="Recipient TRON address" />
          </div>
          <button class="btn btn-success" id="redeemVoucherBtn">🔄 Redeem Voucher</button>
          <button class="btn btn-info" id="checkVoucherBtn">🔍 Check Voucher Info</button>
        </div>
      </div>
    </div>

    <!-- Balance Check Tab -->
    <div id="balance" class="tab-content">
      <div class="alert alert-info">
        <strong>💳 Balance Management</strong><br>
        Check on-chain USDT balances, off-chain balances, and transaction history.
      </div>

      <div class="row">
        <div class="col">
          <h3>On-Chain Balance</h3>
          <div class="form-group">
            <label>Check Main Wallet Balance:</label>
            <button class="btn btn-primary" id="checkMainBalanceBtn">💰 Check Main Wallet</button>
          </div>

          <h3>Off-Chain Balance</h3>
          <div class="form-group">
            <label>Address to Check:</label>
            <input id="balanceCheckAddress" type="text" placeholder="TRON address" />
          </div>
          <button class="btn btn-info" id="checkOffChainBalanceBtn">💳 Check Off-Chain Balance</button>
          <button class="btn btn-warning" id="checkAllBalancesBtn">📋 Check All Balances</button>
        </div>
        <div class="col">
          <div class="feature-card">
            <h3>Balance Types:</h3>
            <ul>
              <li><strong>On-Chain USDT:</strong> Real USDT in wallet</li>
              <li><strong>Off-Chain USDT:</strong> App-based tokens</li>
              <li><strong>TRX Balance:</strong> For gas fees</li>
              <li><strong>Transaction History:</strong> All operations</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Network Configuration Tab -->
    <div id="network" class="tab-content">
      <div class="alert alert-info">
        <strong>🌐 Network Configuration</strong><br>
        Switch between TRON Testnet and Mainnet. Choose testnet for development or mainnet for production.
      </div>

      <div class="row">
        <div class="col">
          <h3>Current Network</h3>
          <div class="feature-card">
            <div id="currentNetworkInfo">
              <p><strong>Network:</strong> <span id="currentNetworkName">Loading...</span></p>
              <p><strong>Contract:</strong> <code id="currentUsdtContract">Loading...</code></p>
              <p><strong>Explorer:</strong> <span id="currentExplorer">Loading...</span></p>
              <p><strong>Main Wallet:</strong> <code id="mainWalletAddress">Loading...</code></p>
              <p><strong>USDT Balance:</strong> <span id="mainWalletUsdt">Loading...</span></p>
              <p><strong>TRX Balance:</strong> <span id="mainWalletTrx">Loading...</span></p>
            </div>
            <button class="btn btn-info" id="refreshNetworkBtn">🔄 Refresh Network Info</button>
          </div>

          <h3>Switch Network</h3>
          <div class="form-group">
            <label>Select Network:</label>
            <select id="networkSelect">
              <option value="testnet">🧪 TRON Nile Testnet (Safe for Development)</option>
              <option value="mainnet">⚠️ TRON Mainnet (Real Funds)</option>
            </select>
          </div>
          <button class="btn btn-warning" id="switchNetworkBtn">🌐 Switch Network</button>
          <button class="btn btn-success" id="getFaucetBtn">💰 Get Testnet Tokens</button>
        </div>
        <div class="col">
          <div class="feature-card">
            <h3>Network Comparison:</h3>
            <h4>🧪 Testnet (Nile)</h4>
            <ul>
              <li>✅ Free tokens from faucet</li>
              <li>✅ Safe for development</li>
              <li>✅ No real monetary value</li>
              <li>✅ Perfect for testing</li>
              <li>✅ Fast transaction confirmation</li>
            </ul>

            <h4>⚠️ Mainnet</h4>
            <ul>
              <li>💰 Real USDT with monetary value</li>
              <li>💰 Real TRX gas fees required</li>
              <li>⚠️ Use with caution</li>
              <li>⚠️ Irreversible transactions</li>
              <li>⚠️ Production environment</li>
            </ul>
          </div>

          <div class="feature-card">
            <h3>Important Notes:</h3>
            <ul>
              <li>🔄 Network switching requires server restart</li>
              <li>💾 Update .env file for permanent changes</li>
              <li>🔍 Always verify network before operations</li>
              <li>💰 Ensure sufficient TRX for gas fees</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Token Info Tab -->
    <div id="token" class="tab-content">
      <div class="alert alert-info">
        <strong>🪙 Token Information</strong><br>
        Add off-chain USDT token to your wallet to see balances. Uses Nile testnet contract for wallet display.
      </div>

      <div class="row">
        <div class="col">
          <h3>Add Token to Wallet</h3>
          <div class="feature-card">
            <h4>Off-Chain USDT Token (OCUSDT)</h4>
            <p><strong>Contract:</strong> <code id="tokenContract">Loading...</code></p>
            <p><strong>Symbol:</strong> OCUSDT</p>
            <p><strong>Decimals:</strong> 6</p>
            <p><strong>Network:</strong> TRON Nile Testnet</p>
            <button class="btn btn-primary" id="getTokenInfoBtn">🪙 Get Token Info</button>
            <button class="btn btn-success" id="copyContractBtn">📋 Copy Contract Address</button>
          </div>

          <h3>Check Token Balance</h3>
          <div class="form-group">
            <label>Address to Check:</label>
            <input id="tokenBalanceAddress" type="text" placeholder="TRON address" />
          </div>
          <button class="btn btn-info" id="checkTokenBalanceBtn">💳 Check Token Balance</button>
          <button class="btn btn-warning" id="syncTokenBalanceBtn">🔄 Sync to Wallet</button>
        </div>
        <div class="col">
          <div class="feature-card">
            <h3>How to Add Token:</h3>
            <ol>
              <li>Open your TRON wallet (TronLink, etc.)</li>
              <li>Go to "Add Token" or "Custom Token"</li>
              <li>Select "TRC20" token type</li>
              <li>Copy contract address from left</li>
              <li>Token details auto-fill (OCUSDT, 6 decimals)</li>
              <li>Confirm to add the token</li>
              <li>Your off-chain USDT balance will be visible</li>
            </ol>
          </div>

          <div class="feature-card">
            <h3>Important Notes:</h3>
            <ul>
              <li>⚠️ Testnet token for display only</li>
              <li>⚠️ Real value backed by fractional reserves</li>
              <li>⚠️ Use external transfer for real USDT</li>
              <li>💡 1:50,000 conversion to real USDT</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Reserve Status Tab -->
    <div id="reserve" class="tab-content">
      <div class="alert alert-success">
        <strong>📊 Reserve Status & System Health</strong><br>
        Monitor fractional reserve ratios, capacity utilization, and system health metrics.
      </div>

      <div class="row">
        <div class="col">
          <button class="btn btn-primary" id="checkReserveBtn">📊 Check Reserve Status</button>
          <button class="btn btn-info" id="refreshReserveBtn">🔄 Refresh Status</button>
        </div>
        <div class="col">
          <div class="feature-card">
            <h3>Reserve Metrics:</h3>
            <ul>
              <li><strong>Reserve Ratio:</strong> 1:50,000</li>
              <li><strong>On-Chain Reserve:</strong> Real USDT backing</li>
              <li><strong>Off-Chain Issued:</strong> Total issued tokens</li>
              <li><strong>Utilization Rate:</strong> Capacity usage</li>
              <li><strong>System Health:</strong> Overall status</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Legacy Functions Tab -->
    <div id="legacy" class="tab-content">
      <div class="alert alert-info">
        <strong>🔧 Legacy Functions</strong><span class="status-badge status-demo">DEMO</span><br>
        Original functions for testing and compatibility.
      </div>

      <div class="row">
        <div class="col">
          <div class="form-group">
            <label>To Address:</label>
            <input id="legacyToAddress" type="text" placeholder="Recipient address" />
          </div>
          <div class="form-group">
            <label>Amount:</label>
            <input id="legacyAmount" type="number" step="0.000001" placeholder="e.g. 0.5" />
          </div>
          <div class="form-group">
            <label>Validity (minutes):</label>
            <input id="legacyValidity" type="number" placeholder="e.g. 10" />
          </div>
          <div>
            <button class="btn btn-primary" id="legacyGenerateBtn">Generate</button>
            <button class="btn btn-info" id="legacyStatusBtn">Status</button>
            <button class="btn btn-success" id="legacyBalanceBtn">Check Balance</button>
            <button class="btn btn-warning" id="legacyDepositBtn">Deposit</button>
          </div>
        </div>
        <div class="col">
          <div class="feature-card">
            <h3>Legacy Functions:</h3>
            <ul>
              <li>Original generate function</li>
              <li>Status checking</li>
              <li>Balance verification</li>
              <li>Deposit functionality</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Output Section -->
    <div style="padding: 20px; background: #f8f9fa; border-top: 1px solid #dee2e6;">
      <h2>📋 Output & Results</h2>
      <div id="output" class="output">Ready. Select a function above to get started.</div>
    </div>
  </div>

  <script src="app.js"></script>
</body>
</html>