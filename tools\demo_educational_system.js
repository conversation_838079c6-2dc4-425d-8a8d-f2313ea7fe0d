#!/usr/bin/env node
import 'dotenv/config';

const API_KEY = process.env.API_KEY;
const BASE_URL = 'http://localhost:3000';

// Helper function to make API requests
async function apiRequest(endpoint, method = 'GET', data = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'x-api-key': API_KEY,
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return { status: response.status, data: result };
  } catch (error) {
    return { status: 500, data: { error: error.message } };
  }
}

async function demonstrateEducationalSystem() {
  console.log('🎓 Educational Off-Chain USDT System Demo');
  console.log('==========================================');
  console.log('Purpose: Demonstrate fractional reserve banking with 1:1 external transfers');
  console.log('Network: TRON Nile Testnet');
  console.log('Reserve Ratio: 1:50,000 (1 real USDT backs 50,000 off-chain USDT)');
  console.log('External Transfer: 1:1 (Educational Demo)\n');
  
  // Step 1: Check initial reserve status
  console.log('📊 Step 1: Checking Reserve Status...');
  const reserveStatus = await apiRequest('/reserve-status');
  if (reserveStatus.status === 200) {
    const reserve = reserveStatus.data.reserve;
    console.log(`✅ On-chain Reserve: ${reserve.onChainReserve}`);
    console.log(`✅ Max Off-chain Capacity: ${reserve.maxOffChainCapacity}`);
    console.log(`✅ Current Utilization: ${reserve.utilizationRate}`);
    console.log(`✅ System Health: ${reserveStatus.data.health.overall}\n`);
  } else {
    console.log('❌ Failed to get reserve status\n');
    return;
  }
  
  // Step 2: Generate off-chain USDT
  console.log('💰 Step 2: Generating Off-Chain USDT...');
  const userWallet = 'TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx'; // Your wallet
  const generateRequest = {
    toAddress: userWallet,
    amount: 2000, // Generate 2000 off-chain USDT
    validityMinutes: 120 // Valid for 2 hours
  };
  
  const generateResult = await apiRequest('/generate', 'POST', generateRequest);
  if (generateResult.status === 200) {
    const result = generateResult.data;
    console.log(`✅ Generated: ${result.amount} off-chain USDT`);
    console.log(`✅ Net Amount: ${result.netAmount} off-chain USDT (after ${result.fee} fee)`);
    console.log(`✅ Transaction ID: ${result.txID}`);
    console.log(`✅ Valid Until: ${result.expiresAt}`);
    console.log(`✅ Reserve Utilization: ${result.reserve.utilizationRate}\n`);
  } else {
    console.log('❌ Failed to generate off-chain USDT:', generateResult.data.error);
    return;
  }
  
  // Step 3: Check off-chain balance
  console.log('💳 Step 3: Checking Off-Chain Balance...');
  const balanceResult = await apiRequest(`/offchain-balance/${userWallet}`);
  if (balanceResult.status === 200) {
    const balance = balanceResult.data;
    console.log(`✅ Off-Chain Balance: ${balance.offChainBalance} USDT`);
    console.log(`✅ Active Transactions: ${balance.transactionCount}\n`);
  } else {
    console.log('❌ Failed to get balance\n');
  }
  
  // Step 4: Check external transfer rates
  console.log('💱 Step 4: Checking External Transfer Rates...');
  const ratesResult = await apiRequest('/external-wallet/rates');
  if (ratesResult.status === 200) {
    const rates = ratesResult.data;
    console.log(`✅ External Transfer Rate: ${rates.externalTransfer.description}`);
    console.log(`✅ Transfer Fee: ${rates.externalTransfer.fee}`);
    console.log(`✅ Educational Note: ${rates.comparison.educationalPurpose}\n`);
  } else {
    console.log('❌ Failed to get rates\n');
  }
  
  // Step 5: Demonstrate external wallet transfer (1:1 conversion)
  console.log('🌍 Step 5: Sending Off-Chain USDT to External Wallet (1:1)...');
  const externalWallet = 'TTestExternalWallet123456789012345678'; // Example external wallet
  const externalTransferRequest = {
    fromAddress: userWallet,
    toAddress: externalWallet,
    offChainAmount: 500, // Send 500 off-chain USDT
    transferType: 'direct'
  };
  
  const externalResult = await apiRequest('/external-wallet/transfer', 'POST', externalTransferRequest);
  if (externalResult.status === 200) {
    const transfer = externalResult.data;
    console.log(`✅ External Transfer Successful!`);
    console.log(`✅ Off-Chain Amount Sent: ${transfer.offChainAmount} USDT`);
    console.log(`✅ Real USDT Transferred: ${transfer.realUsdtAmount} USDT`);
    console.log(`✅ Conversion Rate: ${transfer.conversionRate}`);
    console.log(`✅ Blockchain Transaction: ${transfer.onChainTxID}`);
    console.log(`✅ External Wallet: ${transfer.toAddress}`);
    console.log(`✅ Status: ${transfer.status}\n`);
  } else {
    console.log('❌ External transfer failed:', externalResult.data.error);
    console.log('Note: This might fail due to invalid address format\n');
  }
  
  // Step 6: Create a transferable voucher
  console.log('🎫 Step 6: Creating Transferable Voucher...');
  const voucherRequest = {
    fromAddress: userWallet,
    amount: 300,
    message: "Educational demo voucher - can be shared with anyone!",
    expiryHours: 24
  };
  
  const voucherResult = await apiRequest('/voucher/create', 'POST', voucherRequest);
  if (voucherResult.status === 200) {
    const voucher = voucherResult.data;
    console.log(`✅ Voucher Created: ${voucher.voucherCode}`);
    console.log(`✅ Amount: ${voucher.netAmount} off-chain USDT`);
    console.log(`✅ Message: "${voucher.message}"`);
    console.log(`✅ Shareable: Anyone can redeem this code for off-chain USDT\n`);
  } else {
    console.log('❌ Failed to create voucher:', voucherResult.data.error);
  }
  
  // Step 7: Check final balances and status
  console.log('📋 Step 7: Final System Status...');
  const finalBalanceResult = await apiRequest(`/offchain-balance/${userWallet}`);
  const finalReserveResult = await apiRequest('/reserve-status');
  
  if (finalBalanceResult.status === 200 && finalReserveResult.status === 200) {
    const finalBalance = finalBalanceResult.data;
    const finalReserve = finalReserveResult.data;
    
    console.log(`✅ Final Off-Chain Balance: ${finalBalance.offChainBalance} USDT`);
    console.log(`✅ Total Transactions: ${finalBalance.transactionCount}`);
    console.log(`✅ System Utilization: ${finalReserve.reserve.utilizationRate}`);
    console.log(`✅ Remaining Capacity: ${finalReserve.reserve.remainingCapacity}\n`);
  }
  
  // Educational Summary
  console.log('🎯 Educational System Summary:');
  console.log('==============================');
  console.log('✅ FRACTIONAL RESERVE DEMONSTRATED:');
  console.log('   • 1 real USDT on-chain backs 50,000 off-chain USDT');
  console.log('   • Off-chain tokens exist only in application database');
  console.log('   • System monitors reserve ratios and capacity limits');
  console.log('');
  console.log('✅ 1:1 EXTERNAL TRANSFERS (EDUCATIONAL):');
  console.log('   • Off-chain USDT can be sent to external wallets as real USDT');
  console.log('   • Full 1:1 conversion rate for educational demonstration');
  console.log('   • Real blockchain transactions occur on TRON testnet');
  console.log('   • Demonstrates how off-chain tokens can be "materialized"');
  console.log('');
  console.log('✅ VOUCHER SYSTEM:');
  console.log('   • Create transferable codes for off-chain USDT');
  console.log('   • Share via SMS, email, QR codes, etc.');
  console.log('   • Recipients redeem for off-chain USDT in the app');
  console.log('');
  console.log('✅ INTERNAL TRANSFERS:');
  console.log('   • Transfer off-chain USDT between app users');
  console.log('   • No blockchain fees for internal transfers');
  console.log('   • Maintains validity periods and expiration');
  console.log('');
  console.log('⚠️  EDUCATIONAL NOTES:');
  console.log('• This system demonstrates fractional reserve banking concepts');
  console.log('• 1:1 external transfers consume real USDT from reserves');
  console.log('• In production, conversion rates would be much lower');
  console.log('• Testnet environment allows for educational experimentation');
  console.log('• Gas fees are minimal on testnet for demonstration purposes');
}

// Run the demonstration
demonstrateEducationalSystem().catch(console.error);
