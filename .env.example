# Main wallet configuration (choose one method)
# Method 1: Direct private key
MAIN_WALLET_PRIVATE_KEY=your_private_key_here

# Method 2: BIP44 mnemonic derivation (recommended)
# MAIN_WALLET_MNEMONIC=your twelve word mnemonic phrase here
# MAIN_WALLET_ACCOUNT_INDEX=0

# Tron node endpoints
TRON_FULL_NODE=https://api.trongrid.io
TRON_SOLIDITY_NODE=https://api.trongrid.io
TRON_EVENT_NODE=https://api.trongrid.io

# USDT-TRC20 contract address (real USDT for reserves)
USDT_CONTRACT_ADDRESS=TXLAQ63Xg1NAzckPwKHvzw7CSEmLMEqcdj

# Off-chain token contract address (Nile testnet token for wallet display)
OFFCHAIN_TOKEN_CONTRACT=TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs

# Redis connection URL
REDIS_URL=redis://localhost:6379

# HTTP server port
PORT=3000

# Simple API key for authentication
API_KEY=b6a9331327e44fdfa6c2ef8872b3c1f0a7d4c9e5f1b2a3c4d5e6f7a8b9c0d1e2
