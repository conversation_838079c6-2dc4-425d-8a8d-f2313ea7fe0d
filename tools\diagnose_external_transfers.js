#!/usr/bin/env node
import 'dotenv/config';
import { TronWeb } from 'tronweb';

const fullNode = process.env.TRON_FULL_NODE;
const solidityNode = process.env.TRON_SOLIDITY_NODE;
const eventNode = process.env.TRON_EVENT_NODE;
const usdtContract = process.env.USDT_CONTRACT_ADDRESS;

// Get wallet details from environment
const mnemonic = process.env.MAIN_WALLET_MNEMONIC;
const accountIndex = parseInt(process.env.MAIN_WALLET_ACCOUNT_INDEX || '0', 10);

async function derivePrivateKeyFromMnemonic(mnemonic, accountIndex = 0) {
  const bip39 = (await import('bip39')).default;
  const hdkey = (await import('hdkey')).default;
  
  const seed = await bip39.mnemonicToSeed(mnemonic);
  const root = hdkey.fromMasterSeed(seed);
  const derivationPath = `m/44'/195'/0'/0/${accountIndex}`;
  const addrNode = root.derive(derivationPath);
  return addrNode.privateKey.toString('hex');
}

async function diagnoseExternalTransfers() {
  console.log('🔍 Diagnosing External Transfer Issues');
  console.log('=====================================\n');
  
  try {
    // Get private key
    const privateKey = await derivePrivateKeyFromMnemonic(mnemonic, accountIndex);
    
    // Initialize TronWeb with private key
    const tronWeb = new TronWeb(fullNode, solidityNode, eventNode, privateKey);
    const mainAddress = TronWeb.address.fromPrivateKey(privateKey);
    
    console.log('📋 Configuration Check:');
    console.log('Network:', fullNode);
    console.log('Main Address:', mainAddress);
    console.log('USDT Contract:', usdtContract);
    console.log('');
    
    // Check TRX balance for gas fees
    console.log('⛽ Gas Fee Check:');
    const trxBalance = await tronWeb.trx.getBalance(mainAddress);
    const trxBalanceFormatted = tronWeb.fromSun(trxBalance);
    console.log('TRX Balance:', trxBalanceFormatted, 'TRX');
    
    if (trxBalanceFormatted < 10) {
      console.log('⚠️  WARNING: Low TRX balance may cause transaction failures');
    } else {
      console.log('✅ Sufficient TRX for gas fees');
    }
    console.log('');
    
    // Check USDT balance
    console.log('💰 USDT Balance Check:');
    const contract = await tronWeb.contract().at(usdtContract);
    const rawBalance = await contract.balanceOf(mainAddress).call();
    const usdtBalance = parseFloat(rawBalance.toString()) / 1000000;
    console.log('USDT Balance:', usdtBalance, 'USDT');
    
    if (usdtBalance < 1) {
      console.log('❌ ERROR: Insufficient USDT balance for transfers');
      return;
    } else {
      console.log('✅ Sufficient USDT for transfers');
    }
    console.log('');
    
    // Test a small transfer to verify functionality
    console.log('🧪 Testing Small Transfer...');
    const testAddress = 'TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx'; // Your own address for testing
    const testAmount = 1; // 1 USDT test
    
    console.log(`Attempting to send ${testAmount} USDT to ${testAddress}...`);
    
    try {
      // Check balance before
      const balanceBefore = await contract.balanceOf(testAddress).call();
      const balanceBeforeFormatted = parseFloat(balanceBefore.toString()) / 1000000;
      console.log('Balance Before:', balanceBeforeFormatted, 'USDT');
      
      // Perform transfer
      const amountSun = Math.floor(testAmount * 1000000); // Convert to 6 decimals
      console.log('Amount in smallest unit:', amountSun);
      
      const txResult = await contract.transfer(testAddress, amountSun).send({
        feeLimit: 100000000, // 100 TRX fee limit
        callValue: 0,
        shouldPollResponse: true
      });
      
      console.log('✅ Transaction sent successfully!');
      console.log('Transaction ID:', txResult);
      
      // Wait a moment for transaction to be processed
      console.log('⏳ Waiting for transaction confirmation...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Check balance after
      const balanceAfter = await contract.balanceOf(testAddress).call();
      const balanceAfterFormatted = parseFloat(balanceAfter.toString()) / 1000000;
      console.log('Balance After:', balanceAfterFormatted, 'USDT');
      
      const difference = balanceAfterFormatted - balanceBeforeFormatted;
      console.log('Balance Change:', difference, 'USDT');
      
      if (difference >= testAmount * 0.99) { // Allow for small rounding
        console.log('✅ Transfer successful - balance updated correctly!');
      } else {
        console.log('❌ Transfer may have failed - balance not updated as expected');
      }
      
      // Get transaction details
      console.log('\n📊 Transaction Details:');
      try {
        const txInfo = await tronWeb.trx.getTransaction(txResult);
        console.log('Transaction Info:', JSON.stringify(txInfo, null, 2));
      } catch (e) {
        console.log('Could not fetch transaction details:', e.message);
      }
      
    } catch (transferError) {
      console.log('❌ Transfer failed:', transferError.message);
      console.log('Full error:', transferError);
      
      // Common error analysis
      if (transferError.message.includes('REVERT')) {
        console.log('💡 Possible causes:');
        console.log('   - Insufficient USDT balance');
        console.log('   - Contract interaction error');
        console.log('   - Invalid recipient address');
      } else if (transferError.message.includes('bandwidth') || transferError.message.includes('energy')) {
        console.log('💡 Possible causes:');
        console.log('   - Insufficient TRX for gas fees');
        console.log('   - Network congestion');
      }
    }
    
  } catch (error) {
    console.log('❌ Diagnosis failed:', error.message);
    console.log('Full error:', error);
  }
}

diagnoseExternalTransfers().catch(console.error);
