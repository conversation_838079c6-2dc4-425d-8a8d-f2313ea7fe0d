# Repository Testing Configuration

## Testing Framework
- **E2E Testing Framework**: Playwright
- **Unit Testing Framework**: Jest with Supertest

## Application Type
- Node.js/Express REST API service for USDT-TRC20 issuance
- Key endpoints: `/health`, `/generate`, `/status/{address}`
- Authentication via `x-api-key` header
- Documentation available at `/docs` (Swagger UI)

## Testing Strategy
- E2E tests focus on API endpoint integration testing
- Unit tests mock external dependencies (Redis, TronWeb)
- All tests should be deterministic and not depend on external services