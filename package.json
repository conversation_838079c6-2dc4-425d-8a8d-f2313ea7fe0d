{"name": "usdt-trc20-issuance-service", "version": "0.1.0", "type": "module", "description": "Off-chain USDT-TRC20 issuance service", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:redis": "node src/testRedis.js", "test:e2e": "npx playwright test --reporter=line"}, "dependencies": {"dotenv": "^10.0.0", "express": "^4.17.1", "express-rate-limit": "^5.2.6", "joi": "^17.4.0", "morgan": "^1.10.0", "node-cache": "^5.1.2", "redis": "^3.1.2", "swagger-ui-express": "^4.1.6", "tronweb": "^6.0.3", "uuid": "^8.3.2", "winston": "^3.3.3"}, "devDependencies": {"@playwright/test": "^1.54.1", "jest": "^27.2.0", "nodemon": "^2.0.7", "playwright": "^1.54.1", "supertest": "^6.1.3"}}