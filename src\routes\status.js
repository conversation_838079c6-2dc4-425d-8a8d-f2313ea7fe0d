import express from 'express';
import redisClient from '../config/redis.js';

const router = express.Router();

router.get('/:address', async (req, res) => {
  const { address } = req.params;
  try {
    const keys = await redisClient.keys(`issue:${address}:*`);
    const records = [];
    for (const key of keys) {
      const rec = await redisClient.get(key);
      records.push(JSON.parse(rec));
    }
    res.json(records);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch status', details: err.message });
  }
});

export default router;