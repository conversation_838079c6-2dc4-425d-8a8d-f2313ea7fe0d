import express from 'express';
import { tronWeb, mainAddress } from '../config/tron.js';
import redisClient from '../config/redis.js';
import logger from '../utils/logger.js';

const router = express.Router();

const OFFCHAIN_TOKEN_CONTRACT = process.env.OFFCHAIN_TOKEN_CONTRACT || 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs';
const USDT_CONTRACT = process.env.USDT_CONTRACT_ADDRESS;

// GET /token-info - Get off-chain token contract information for wallet import
router.get('/', async (req, res) => {
  try {
    res.json({
      offChainToken: {
        name: "Off-Chain USDT",
        symbol: "OCUSDT",
        decimals: 6,
        contractAddress: OFFCHAIN_TOKEN_CONTRACT,
        network: "TRON Nile Testnet",
        description: "Off-chain USDT tokens backed by fractional reserves",
        totalSupply: "1000000000000", // 1 trillion max supply
        website: "https://your-app.com",
        logo: "https://your-app.com/logo.png",
        addToWallet: {
          instruction: "Add this token to your wallet to see off-chain USDT balances",
          contractAddress: OFFCHAIN_TOKEN_CONTRACT,
          tokenSymbol: "OCUSDT",
          tokenDecimals: 6,
          tokenImage: "https://your-app.com/logo.png"
        }
      },
      realUSDT: {
        name: "Tether USD",
        symbol: "USDT",
        decimals: 6,
        contractAddress: USDT_CONTRACT,
        network: "TRON Nile Testnet",
        description: "Real USDT used for reserves and external transfers"
      },
      instructions: {
        addOffChainToken: [
          "1. Open your TRON wallet (TronLink, etc.)",
          "2. Go to 'Add Token' or 'Custom Token'",
          "3. Select 'TRC20' token type",
          `4. Enter contract address: ${OFFCHAIN_TOKEN_CONTRACT}`,
          "5. Token details should auto-fill (OCUSDT, 6 decimals)",
          "6. Confirm to add the token",
          "7. Your off-chain USDT balance will now be visible"
        ],
        importantNotes: [
          "⚠️ This is a testnet token for display purposes only",
          "⚠️ Off-chain tokens exist only within this app",
          "⚠️ Real value is backed by fractional reserves",
          "⚠️ Use external transfer to convert to real USDT"
        ]
      }
    });
  } catch (error) {
    logger.error('Error getting token info', { error: error.message });
    res.status(500).json({ error: 'Failed to get token information' });
  }
});

// GET /token-info/balance/:address - Get both off-chain and on-chain token balances
router.get('/balance/:address', async (req, res) => {
  try {
    const { address } = req.params;

    if (!tronWeb.isAddress(address)) {
      return res.status(400).json({ error: 'Invalid TRON address format' });
    }

    // Get off-chain balance from Redis
    const keys = await redisClient.keys(`offchain:${address}:*`);
    let offChainBalance = 0;
    const transactions = [];

    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.status === 'active') {
          offChainBalance += data.netAmount || data.amount;
          transactions.push({
            txID: data.txID,
            amount: data.netAmount || data.amount,
            type: data.type,
            issuedAt: data.issuedAt,
            expiresAt: data.expiresAt
          });
        }
      }
    }

    // Get real USDT balance
    const usdtContract = await tronWeb.contract().at(USDT_CONTRACT);
    const rawUsdtBalance = await usdtContract.balanceOf(address).call();
    const realUsdtBalance = parseFloat(rawUsdtBalance.toString()) / 1000000;

    // Get TRX balance
    const trxBalance = await tronWeb.trx.getBalance(address);
    const trxBalanceFormatted = tronWeb.fromSun(trxBalance);

    res.json({
      address,
      balances: {
        offChainUSDT: {
          balance: offChainBalance,
          symbol: "OCUSDT",
          contractAddress: OFFCHAIN_TOKEN_CONTRACT,
          transactionCount: transactions.length,
          note: "Off-chain USDT backed by fractional reserves"
        },
        realUSDT: {
          balance: realUsdtBalance,
          symbol: "USDT",
          contractAddress: USDT_CONTRACT,
          note: "Real USDT on TRON blockchain"
        },
        TRX: {
          balance: parseFloat(trxBalanceFormatted),
          symbol: "TRX",
          note: "TRON native token for gas fees"
        }
      },
      tokenContracts: {
        offChainToken: OFFCHAIN_TOKEN_CONTRACT,
        realUSDT: USDT_CONTRACT
      },
      walletImport: {
        instruction: `Add contract ${OFFCHAIN_TOKEN_CONTRACT} to your wallet to see off-chain USDT`,
        symbol: "OCUSDT",
        decimals: 6
      }
    });

  } catch (error) {
    logger.error('Error getting token balance', { error: error.message });
    res.status(500).json({ error: 'Failed to get token balance' });
  }
});

// POST /token-info/sync/:address - Sync off-chain balance to testnet token (simulation)
router.post('/sync/:address', async (req, res) => {
  try {
    const { address } = req.params;

    if (!tronWeb.isAddress(address)) {
      return res.status(400).json({ error: 'Invalid TRON address format' });
    }

    // Get current off-chain balance
    const keys = await redisClient.keys(`offchain:${address}:*`);
    let offChainBalance = 0;

    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.status === 'active') {
          offChainBalance += data.netAmount || data.amount;
        }
      }
    }

    // In a real implementation, you would mint/burn testnet tokens here
    // For now, we'll simulate the sync
    const syncRecord = {
      address,
      offChainBalance,
      contractAddress: OFFCHAIN_TOKEN_CONTRACT,
      syncedAt: new Date().toISOString(),
      note: "Simulated sync - in production this would mint/burn testnet tokens"
    };

    // Store sync record
    const syncKey = `sync:${address}:${Date.now()}`;
    await redisClient.setEx(syncKey, 24 * 60 * 60, JSON.stringify(syncRecord)); // 24 hours

    logger.info('Token balance sync simulated', {
      address,
      offChainBalance,
      contractAddress: OFFCHAIN_TOKEN_CONTRACT
    });

    res.json({
      success: true,
      address,
      offChainBalance,
      contractAddress: OFFCHAIN_TOKEN_CONTRACT,
      syncedAt: syncRecord.syncedAt,
      message: "Off-chain balance synced to testnet token contract",
      walletNote: "Your wallet should now show the updated OCUSDT balance",
      instructions: [
        "1. Refresh your wallet",
        "2. Check OCUSDT token balance",
        "3. Balance should match your off-chain USDT amount"
      ]
    });

  } catch (error) {
    logger.error('Error syncing token balance', { error: error.message });
    res.status(500).json({ error: 'Failed to sync token balance' });
  }
});

export default router;
