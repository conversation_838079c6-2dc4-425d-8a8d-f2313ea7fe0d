#!/usr/bin/env node
import 'dotenv/config';

const API_KEY = process.env.API_KEY;
const BASE_URL = 'http://localhost:3000';

// Helper function to make API requests
async function apiRequest(endpoint, method = 'GET', data = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'x-api-key': API_KEY,
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return { status: response.status, data: result };
  } catch (error) {
    return { status: 500, data: { error: error.message } };
  }
}

async function testFractionalConversion() {
  console.log('🧮 Testing 1:50,000 Fractional Reserve Conversion');
  console.log('=================================================\n');
  
  const testWallet = 'TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx';
  const externalWallet = 'TLsV52sRDL79HXGGm9yzwDeVXddxjHRvUb';
  
  // Step 1: Check conversion rates
  console.log('💱 Step 1: Checking New Conversion Rates...');
  const ratesResult = await apiRequest('/external-wallet/rates');
  if (ratesResult.status === 200) {
    const rates = ratesResult.data;
    console.log(`✅ External Transfer Rate: ${rates.externalTransfer.description}`);
    console.log(`✅ Conversion Rate: ${rates.externalTransfer.rate}`);
    console.log(`✅ Fee: ${rates.externalTransfer.fee}`);
    console.log(`✅ Note: ${rates.externalTransfer.note}\n`);
  } else {
    console.log('❌ Failed to get rates\n');
    return;
  }
  
  // Step 2: Generate off-chain USDT
  console.log('💰 Step 2: Generating Off-Chain USDT...');
  const generateResult = await apiRequest('/generate', 'POST', {
    toAddress: testWallet,
    amount: 100000, // Generate 100,000 off-chain USDT
    validityMinutes: 60
  });
  
  if (generateResult.status === 200) {
    const result = generateResult.data;
    console.log(`✅ Generated: ${result.netAmount} off-chain USDT`);
    console.log(`✅ This should convert to: ${result.netAmount / 50000} real USDT\n`);
  } else {
    console.log('❌ Failed to generate off-chain USDT\n');
    return;
  }
  
  // Step 3: Test external transfer with fractional conversion
  console.log('🌍 Step 3: Testing External Transfer (1:50,000 Conversion)...');
  const transferResult = await apiRequest('/external-wallet/transfer', 'POST', {
    fromAddress: testWallet,
    toAddress: externalWallet,
    offChainAmount: 50000, // Send 50,000 off-chain USDT
    transferType: 'direct'
  });
  
  if (transferResult.status === 200) {
    const transfer = transferResult.data;
    console.log(`✅ External Transfer Successful!`);
    console.log(`✅ Off-Chain Amount Sent: ${transfer.offChainAmount} USDT`);
    console.log(`✅ Real USDT Transferred: ${transfer.realUsdtAmount} USDT`);
    console.log(`✅ Conversion Rate: ${transfer.conversionRate}`);
    console.log(`✅ Conversion Description: ${transfer.conversion.description}`);
    console.log(`✅ Blockchain TX: ${transfer.onChainTxID}`);
    console.log(`✅ Explorer: https://nile.tronscan.org/#/transaction/${transfer.onChainTxID}\n`);
    
    // Verify the math
    const expectedRealUSDT = (transfer.offChainAmount - (transfer.offChainAmount * 0.01)) / 50000;
    console.log(`🧮 Math Verification:`);
    console.log(`   Off-chain amount: ${transfer.offChainAmount}`);
    console.log(`   After 1% fee: ${transfer.offChainAmount * 0.99}`);
    console.log(`   Divided by 50,000: ${expectedRealUSDT.toFixed(6)}`);
    console.log(`   Actual sent: ${transfer.realUsdtAmount}`);
    console.log(`   ✅ Math checks out: ${Math.abs(expectedRealUSDT - transfer.realUsdtAmount) < 0.000001 ? 'YES' : 'NO'}\n`);
  } else {
    console.log('❌ External transfer failed:', transferResult.data.error);
    console.log('Details:', JSON.stringify(transferResult.data, null, 2));
  }
  
  // Step 4: Test token information
  console.log('🪙 Step 4: Testing Token Information...');
  const tokenInfoResult = await apiRequest('/token-info');
  if (tokenInfoResult.status === 200) {
    const tokenInfo = tokenInfoResult.data;
    console.log(`✅ Off-Chain Token Contract: ${tokenInfo.offChainToken.contractAddress}`);
    console.log(`✅ Token Symbol: ${tokenInfo.offChainToken.symbol}`);
    console.log(`✅ Token Name: ${tokenInfo.offChainToken.name}`);
    console.log(`✅ Network: ${tokenInfo.offChainToken.network}\n`);
  } else {
    console.log('❌ Failed to get token info\n');
  }
  
  // Step 5: Test token balance check
  console.log('💳 Step 5: Testing Token Balance Check...');
  const tokenBalanceResult = await apiRequest(`/token-info/balance/${testWallet}`);
  if (tokenBalanceResult.status === 200) {
    const balances = tokenBalanceResult.data.balances;
    console.log(`✅ Off-Chain USDT (${balances.offChainUSDT.symbol}): ${balances.offChainUSDT.balance}`);
    console.log(`✅ Real USDT: ${balances.realUSDT.balance}`);
    console.log(`✅ TRX: ${balances.TRX.balance}`);
    console.log(`✅ Contract for wallet import: ${tokenBalanceResult.data.tokenContracts.offChainToken}\n`);
  } else {
    console.log('❌ Failed to get token balance\n');
  }
  
  console.log('📋 Test Summary:');
  console.log('================');
  console.log('✅ FRACTIONAL RESERVE CONVERSION:');
  console.log('   • 50,000 off-chain USDT = 1 real USDT');
  console.log('   • Maintains system sustainability');
  console.log('   • Proper fractional reserve banking model');
  console.log('');
  console.log('✅ TOKEN WALLET INTEGRATION:');
  console.log('   • Off-chain tokens use testnet contract address');
  console.log('   • Users can add OCUSDT token to wallets');
  console.log('   • Balances visible without manual import');
  console.log('   • Testnet token for display purposes');
  console.log('');
  console.log('✅ SYSTEM IMPROVEMENTS:');
  console.log('   • Realistic conversion ratios');
  console.log('   • Wallet-friendly token display');
  console.log('   • Maintains fractional reserve model');
  console.log('   • Educational and practical value');
  console.log('');
  console.log('🎯 NEXT STEPS FOR USERS:');
  console.log('   1. Add OCUSDT token to wallet using contract address');
  console.log('   2. Generate off-chain USDT in the app');
  console.log('   3. See balance in wallet automatically');
  console.log('   4. Use external transfer for real USDT (1:50,000 ratio)');
}

// Run the test
testFractionalConversion().catch(console.error);
