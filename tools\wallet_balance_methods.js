#!/usr/bin/env node
import 'dotenv/config';

const API_KEY = process.env.API_KEY;
const BASE_URL = 'http://localhost:3000';

// Helper function to make API requests
async function apiRequest(endpoint, method = 'GET', data = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'x-api-key': API_KEY,
      'Content-Type': 'application/json'
    }
  };
  
  if (data) {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return { status: response.status, data: result };
  } catch (error) {
    return { status: 500, data: { error: error.message } };
  }
}

async function demonstrateWalletBalanceMethods() {
  console.log('💰 Wallet Balance Increase Methods Demo');
  console.log('=======================================');
  console.log('Testing different ways to increase external wallet balances\n');
  
  const sourceWallet = 'TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx'; // Your main wallet
  const targetWallet = 'TTestWallet123456789012345678901234'; // Example target wallet
  
  console.log('🎯 Method 1: Direct External Transfer (Real USDT)');
  console.log('==================================================');
  console.log('This method sends real USDT to external wallets via blockchain\n');
  
  // Step 1: Generate off-chain USDT in source wallet
  console.log('💰 Step 1: Generating Off-Chain USDT in Source Wallet...');
  const generateRequest = {
    toAddress: sourceWallet,
    amount: 200,
    validityMinutes: 60
  };
  
  const generateResult = await apiRequest('/generate', 'POST', generateRequest);
  if (generateResult.status === 200) {
    console.log(`✅ Generated: ${generateResult.data.netAmount} off-chain USDT`);
    console.log(`✅ Source wallet now has off-chain USDT to transfer\n`);
  } else {
    console.log('❌ Failed to generate off-chain USDT');
    return;
  }
  
  // Step 2: Transfer off-chain USDT to external wallet as real USDT
  console.log('🌍 Step 2: Transferring to External Wallet...');
  console.log(`From: ${sourceWallet}`);
  console.log(`To: ${targetWallet}`);
  
  const transferRequest = {
    fromAddress: sourceWallet,
    toAddress: targetWallet,
    offChainAmount: 100,
    transferType: 'direct'
  };
  
  const transferResult = await apiRequest('/external-wallet/transfer', 'POST', transferRequest);
  if (transferResult.status === 200) {
    const transfer = transferResult.data;
    console.log(`✅ Transfer Successful!`);
    console.log(`✅ Off-Chain Amount: ${transfer.offChainAmount} USDT`);
    console.log(`✅ Real USDT Sent: ${transfer.realUsdtAmount} USDT`);
    console.log(`✅ Blockchain TX: ${transfer.onChainTxID}`);
    console.log(`✅ Target Wallet Balance Increased: ${transfer.balanceVerification?.verified ? 'YES' : 'PENDING'}`);
    console.log(`✅ Explorer: https://nile.tronscan.org/#/transaction/${transfer.onChainTxID}\n`);
  } else {
    console.log('❌ Transfer failed:', transferResult.data.error);
    console.log('Note: This might fail due to invalid address format\n');
  }
  
  console.log('🎫 Method 2: Voucher System (Transferable Codes)');
  console.log('=================================================');
  console.log('Create codes that can be shared and redeemed by external users\n');
  
  // Step 3: Create transferable voucher
  console.log('📝 Step 3: Creating Transferable Voucher...');
  const voucherRequest = {
    fromAddress: sourceWallet,
    amount: 75,
    message: "Redeemable USDT voucher - share this code!",
    expiryHours: 24
  };
  
  const voucherResult = await apiRequest('/voucher/create', 'POST', voucherRequest);
  if (voucherResult.status === 200) {
    const voucher = voucherResult.data;
    console.log(`✅ Voucher Created: ${voucher.voucherCode}`);
    console.log(`✅ Value: ${voucher.netAmount} off-chain USDT`);
    console.log(`✅ Message: "${voucher.message}"`);
    console.log(`✅ How to use: Share this code with anyone`);
    console.log(`✅ Redemption: They use /voucher/redeem endpoint\n`);
    
    // Demonstrate voucher redemption
    console.log('🔄 Step 4: Demonstrating Voucher Redemption...');
    const redeemRequest = {
      voucherCode: voucher.voucherCode,
      toAddress: targetWallet
    };
    
    const redeemResult = await apiRequest('/voucher/redeem', 'POST', redeemRequest);
    if (redeemResult.status === 200) {
      const redemption = redeemResult.data;
      console.log(`✅ Voucher Redeemed Successfully!`);
      console.log(`✅ Redeemed by: ${redemption.toAddress}`);
      console.log(`✅ Amount: ${redemption.amount} off-chain USDT`);
      console.log(`✅ Target wallet now has off-chain USDT balance\n`);
    } else {
      console.log('❌ Voucher redemption failed');
    }
  } else {
    console.log('❌ Failed to create voucher');
  }
  
  console.log('🔄 Method 3: Direct Off-Chain Generation');
  console.log('========================================');
  console.log('Generate off-chain USDT directly in target wallet\n');
  
  // Step 5: Generate off-chain USDT directly in target wallet
  console.log('💰 Step 5: Generating Off-Chain USDT Directly in Target Wallet...');
  const directGenerateRequest = {
    toAddress: targetWallet,
    amount: 150,
    validityMinutes: 120
  };
  
  const directResult = await apiRequest('/generate', 'POST', directGenerateRequest);
  if (directResult.status === 200) {
    console.log(`✅ Generated: ${directResult.data.netAmount} off-chain USDT`);
    console.log(`✅ Directly in target wallet: ${targetWallet}`);
    console.log(`✅ Target wallet now has off-chain balance\n`);
  } else {
    console.log('❌ Direct generation failed:', directResult.data.error);
  }
  
  // Step 6: Check final balances
  console.log('📊 Step 6: Checking Final Balances...');
  const targetBalanceResult = await apiRequest(`/offchain-balance/${targetWallet}`);
  if (targetBalanceResult.status === 200) {
    const balance = targetBalanceResult.data;
    console.log(`✅ Target Wallet Off-Chain Balance: ${balance.offChainBalance} USDT`);
    console.log(`✅ Transaction Count: ${balance.transactionCount}\n`);
  }
  
  console.log('📋 Summary of Methods:');
  console.log('======================');
  console.log('');
  console.log('✅ METHOD 1 - EXTERNAL TRANSFER (Real USDT):');
  console.log('   • Converts off-chain USDT → real USDT');
  console.log('   • Sends via blockchain to external wallet');
  console.log('   • 1:1 conversion rate (educational demo)');
  console.log('   • Increases real USDT balance in external wallet');
  console.log('   • Consumes gas fees');
  console.log('');
  console.log('✅ METHOD 2 - VOUCHER SYSTEM:');
  console.log('   • Creates transferable codes');
  console.log('   • Can be shared via SMS, email, QR codes');
  console.log('   • Recipients redeem for off-chain USDT');
  console.log('   • No blockchain fees');
  console.log('   • Requires app integration for redemption');
  console.log('');
  console.log('✅ METHOD 3 - DIRECT GENERATION:');
  console.log('   • Generate off-chain USDT directly in any wallet');
  console.log('   • No transfer needed');
  console.log('   • Backed by fractional reserves');
  console.log('   • Can be used within app or sent externally');
  console.log('');
  console.log('🎯 RECOMMENDED APPROACH:');
  console.log('   For external wallets: Use Method 1 (External Transfer)');
  console.log('   For app users: Use Method 2 (Vouchers) or Method 3 (Direct)');
  console.log('   For gifts/payments: Use Method 2 (Vouchers)');
}

// Run the demonstration
demonstrateWalletBalanceMethods().catch(console.error);
