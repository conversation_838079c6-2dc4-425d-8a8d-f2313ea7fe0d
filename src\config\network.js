import 'dotenv/config';

// Network configuration
const NETWORK = process.env.NETWORK || 'testnet';

// Network configurations
const NETWORKS = {
  testnet: {
    name: 'TRON Nile Testnet',
    fullNode: process.env.TESTNET_FULL_NODE || 'https://nile.trongrid.io',
    solidityNode: process.env.TESTNET_SOLIDITY_NODE || 'https://nile.trongrid.io',
    eventNode: process.env.TESTNET_EVENT_NODE || 'https://nile.trongrid.io',
    usdtContract: process.env.TESTNET_USDT_CONTRACT || 'TXLAQ63Xg1NAzckPwKHvzw7CSEmLMEqcdj',
    explorer: 'https://nile.tronscan.org',
    faucet: 'https://nileex.io/join/getJoinPage',
    description: 'Testnet for development and testing',
    isMainnet: false
  },
  mainnet: {
    name: 'TRON Mainnet',
    fullNode: process.env.MAINNET_FULL_NODE || 'https://api.trongrid.io',
    solidityNode: process.env.MAINNET_SOLIDITY_NODE || 'https://api.trongrid.io',
    eventNode: process.env.MAINNET_EVENT_NODE || 'https://api.trongrid.io',
    usdtContract: process.env.MAINNET_USDT_CONTRACT || 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t',
    explorer: 'https://tronscan.org',
    faucet: null,
    description: 'Production mainnet with real value',
    isMainnet: true
  }
};

// Get current network configuration
export function getNetworkConfig() {
  const config = NETWORKS[NETWORK];
  if (!config) {
    throw new Error(`Invalid network: ${NETWORK}. Must be 'testnet' or 'mainnet'`);
  }
  return {
    ...config,
    network: NETWORK
  };
}

// Get all available networks
export function getAllNetworks() {
  return Object.keys(NETWORKS).map(key => ({
    key,
    ...NETWORKS[key]
  }));
}

// Switch network (for dynamic switching)
export function switchNetwork(newNetwork) {
  if (!NETWORKS[newNetwork]) {
    throw new Error(`Invalid network: ${newNetwork}. Must be 'testnet' or 'mainnet'`);
  }
  
  // Update environment variables
  process.env.NETWORK = newNetwork;
  process.env.TRON_FULL_NODE = NETWORKS[newNetwork].fullNode;
  process.env.TRON_SOLIDITY_NODE = NETWORKS[newNetwork].solidityNode;
  process.env.TRON_EVENT_NODE = NETWORKS[newNetwork].eventNode;
  process.env.USDT_CONTRACT_ADDRESS = NETWORKS[newNetwork].usdtContract;
  
  return getNetworkConfig();
}

// Validate network configuration
export function validateNetworkConfig() {
  const config = getNetworkConfig();
  const required = ['fullNode', 'solidityNode', 'eventNode', 'usdtContract'];
  
  for (const field of required) {
    if (!config[field]) {
      throw new Error(`Missing required network configuration: ${field}`);
    }
  }
  
  return true;
}

// Get transaction explorer URL
export function getExplorerUrl(txId) {
  const config = getNetworkConfig();
  return `${config.explorer}/#/transaction/${txId}`;
}

// Get address explorer URL
export function getAddressExplorerUrl(address) {
  const config = getNetworkConfig();
  return `${config.explorer}/#/address/${address}`;
}

// Initialize network configuration
export function initializeNetwork() {
  try {
    validateNetworkConfig();
    const config = getNetworkConfig();
    
    console.log(`🌐 Network Configuration:`);
    console.log(`   Network: ${config.name} (${config.network})`);
    console.log(`   Full Node: ${config.fullNode}`);
    console.log(`   USDT Contract: ${config.usdtContract}`);
    console.log(`   Explorer: ${config.explorer}`);
    console.log(`   Is Mainnet: ${config.isMainnet ? 'YES ⚠️' : 'NO'}`);
    
    if (config.isMainnet) {
      console.log(`⚠️  WARNING: Running on MAINNET - real funds will be used!`);
    } else {
      console.log(`✅ Running on testnet - safe for development`);
      if (config.faucet) {
        console.log(`💰 Testnet faucet: ${config.faucet}`);
      }
    }
    
    return config;
  } catch (error) {
    console.error(`❌ Network configuration error: ${error.message}`);
    throw error;
  }
}

// Export current network config as default
export default getNetworkConfig();
