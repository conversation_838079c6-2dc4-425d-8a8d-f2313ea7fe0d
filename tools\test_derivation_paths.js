#!/usr/bin/env node
import 'dotenv/config';
import { TronWeb } from 'tronweb';
import bip39 from 'bip39';
import hdkey from 'hdkey';

const targetPrivateKey = '52dcdf5fa6d00c36d611d95aa0c11e77121ddcfaf2a7e07d6ab3edffddc202d4';
const targetAddress = 'TYsvYZx9BPk2Qknut9654tpiNGxhgAaGmx';
const mnemonic = 'spoon vibrant valley brick tiger cook million asthma village clever remember become';

async function testDerivationPath(mnemonic, path) {
  if (!bip39.validateMnemonic(mnemonic)) {
    throw new Error('Invalid mnemonic phrase');
  }
  const seed = await bip39.mnemonicToSeed(mnemonic);
  const root = hdkey.fromMasterSeed(seed);
  const addrNode = root.derive(path);
  return {
    privateKey: addrNode.privateKey.toString('hex'),
    address: TronWeb.address.fromPrivateKey(addrNode.privateKey.toString('hex'))
  };
}

async function testAllPaths() {
  console.log('🔍 Testing different derivation paths...');
  console.log('Target Private Key:', targetPrivateKey);
  console.log('Target Address:', targetAddress);
  console.log('Mnemonic:', mnemonic);
  console.log('');

  // Common derivation paths for TRON and other cryptocurrencies
  const pathsToTest = [
    // Standard TRON BIP44 paths
    "m/44'/195'/0'/0/0",
    "m/44'/195'/0'/0/1", 
    "m/44'/195'/0'/0/2",
    "m/44'/195'/1'/0/0",
    "m/44'/195'/1'/0/1",
    
    // Alternative TRON paths
    "m/44'/195'/0'/0",
    "m/44'/195'/0'/1",
    "m/44'/195'/1'/0",
    "m/44'/195'/1'/1",
    
    // Ethereum-style paths (sometimes used)
    "m/44'/60'/0'/0/0",
    "m/44'/60'/0'/0/1",
    
    // Bitcoin-style paths
    "m/44'/0'/0'/0/0",
    "m/44'/0'/0'/0/1",
    
    // Legacy paths
    "m/44'/195'/0/0",
    "m/44'/195'/0/1",
    "m/44'/195'/1/0",
    "m/44'/195'/1/1",
    
    // Simple paths
    "m/0'/0/0",
    "m/0'/0/1",
    "m/0/0",
    "m/0/1",
    "m/1",
    "m/0"
  ];

  for (const path of pathsToTest) {
    try {
      const result = await testDerivationPath(mnemonic, path);
      
      const privateKeyMatch = result.privateKey === targetPrivateKey;
      const addressMatch = result.address === targetAddress;
      
      console.log(`Path: ${path}`);
      console.log(`  Private Key: ${result.privateKey} ${privateKeyMatch ? '✅' : '❌'}`);
      console.log(`  Address: ${result.address} ${addressMatch ? '✅' : '❌'}`);
      
      if (privateKeyMatch && addressMatch) {
        console.log('');
        console.log('🎉 FOUND CORRECT DERIVATION PATH!');
        console.log('================================');
        console.log('Correct Path:', path);
        console.log('Private Key:', result.privateKey);
        console.log('Address:', result.address);
        return path;
      }
      console.log('');
    } catch (error) {
      console.error(`Error with path ${path}:`, error.message);
    }
  }
  
  console.log('❌ Could not find matching derivation path');
  return null;
}

testAllPaths().catch(console.error);
