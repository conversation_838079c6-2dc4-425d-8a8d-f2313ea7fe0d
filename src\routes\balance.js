import express from 'express';
import { tronWeb, mainAddress, networkConfig } from '../config/tron.js';
import logger from '../utils/logger.js';

const router = express.Router();
const USDT_CONTRACT = networkConfig.usdtContract;

// GET /balance: return main wallet USDT balance
router.get('/', async (req, res) => {
  try {
    // Get TRX balance first
    const trxBalance = await tronWeb.trx.getBalance(mainAddress);
    const trxBalanceFormatted = tronWeb.fromSun(trxBalance);

    // Get USDT balance
    const contract = await tronWeb.contract().at(USDT_CONTRACT);
    const rawBalance = await contract.balanceOf(mainAddress).call();

    // USDT uses 6 decimals, not 18 like TRX
    let usdtBalance = 0;
    if (rawBalance) {
      const balanceString = rawBalance.toString();
      usdtBalance = parseFloat(balanceString) / 1000000; // 6 decimals for USDT
    }

    res.json({
      address: mainAddress,
      trx_balance: trxBalanceFormatted,
      usdt_balance: usdtBalance,
      balance: usdtBalance // Keep for backward compatibility
    });
  } catch (err) {
    logger.error('Balance fetch error', { error: err.message });
    res.status(500).json({ error: 'Failed to fetch balance', details: err.message });
  }
});

export default router;