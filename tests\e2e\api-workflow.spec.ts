import { test, expect } from '@playwright/test';

test.describe('End-to-End API Workflow', () => {
  const validApiKey = process.env.API_KEY || 'b6a9331327e44fdfa6c2ef8872b3c1f0a7d4c9e5f1b2a3c4d5e6f7a8b9c0d1e2';

  test('complete issuance workflow: generate → verify status → check consistency', async ({ request }) => {
    const testAddress = 'TWORKFLOW123';
    const testAmount = 1.5;
    const testValidity = 20;

    // Step 1: Verify initial state - no issuances for this address
    const initialStatusResponse = await request.get(`/status/${testAddress}`, {
      headers: {
        'x-api-key': validApiKey
      }
    });

    expect(initialStatusResponse.status()).toBe(200);
    const initialStatus = await initialStatusResponse.json();
    expect(Array.isArray(initialStatus)).toBe(true);
    const initialCount = initialStatus.length;

    // Step 2: Generate new issuance
    const generateResponse = await request.post('/generate', {
      headers: {
        'x-api-key': validApiKey
      },
      data: {
        toAddress: testAddress,
        amount: testAmount,
        validityMinutes: testValidity
      }
    });

    // Handle both success and service unavailable scenarios
    if (generateResponse.status() === 200) {
      const generateBody = await generateResponse.json();
      
      // Verify generate response structure
      expect(generateBody).toHaveProperty('txID');
      expect(generateBody).toHaveProperty('amount');
      expect(generateBody).toHaveProperty('expiresAt');
      expect(generateBody.amount).toBe(testAmount);
      
      // Step 3: Verify the issuance appears in status
      const updatedStatusResponse = await request.get(`/status/${testAddress}`, {
        headers: {
          'x-api-key': validApiKey
        }
      });

      expect(updatedStatusResponse.status()).toBe(200);
      const updatedStatus = await updatedStatusResponse.json();
      expect(Array.isArray(updatedStatus)).toBe(true);
      expect(updatedStatus.length).toBe(initialCount + 1);

      // Step 4: Find and verify the created issuance
      const createdIssuance = updatedStatus.find(issuance => issuance.txID === generateBody.txID);
      expect(createdIssuance).toBeDefined();
      
      if (createdIssuance) {
        expect(createdIssuance.amount).toBe(testAmount);
        expect(createdIssuance.expiresAt).toBe(generateBody.expiresAt);
        
        // Verify the expiration time is approximately correct
        const expectedExpiry = new Date(Date.now() + testValidity * 60 * 1000);
        const actualExpiry = new Date(createdIssuance.expiresAt);
        const timeDiff = Math.abs(actualExpiry.getTime() - expectedExpiry.getTime());
        expect(timeDiff).toBeLessThan(30000); // Within 30 seconds tolerance
      }

      // Step 5: Verify other addresses are not affected
      const otherAddressResponse = await request.get('/status/TOTHERADDR123', {
        headers: {
          'x-api-key': validApiKey
        }
      });

      // Should be 200 or 429 (rate limited)
      expect([200, 429]).toContain(otherAddressResponse.status());
      
      if (otherAddressResponse.status() === 200) {
        const otherAddressStatus = await otherAddressResponse.json();
        expect(Array.isArray(otherAddressStatus)).toBe(true);
        
        // Should not contain our txID
        const foundInOtherAddress = otherAddressStatus.some(issuance => issuance.txID === generateBody.txID);
        expect(foundInOtherAddress).toBe(false);
      }

    } else if (generateResponse.status() === 500) {
      // Service unavailable scenario - expected in test environment
      const errorBody = await generateResponse.json();
      expect(errorBody).toHaveProperty('error');
      expect(errorBody.error).toBe('Failed to generate issuance');
      
      console.log('Note: Generate endpoint returned 500 - likely due to TRON service configuration in test environment');
      
    } else {
      throw new Error(`Unexpected status code from generate endpoint: ${generateResponse.status()}`);
    }
  });

  test('multiple issuances for same address', async ({ request }) => {
    const testAddress = 'TMULTIPLE123';
    const issuanceData = [
      { amount: 0.5, validityMinutes: 15 },
      { amount: 1.0, validityMinutes: 30 },
      { amount: 2.0, validityMinutes: 45 }
    ];

    const createdTxIDs = [];

    // Create multiple issuances
    for (const data of issuanceData) {
      const response = await request.post('/generate', {
        headers: {
          'x-api-key': validApiKey
        },
        data: {
          toAddress: testAddress,
          ...data
        }
      });

      if (response.status() === 200) {
        const body = await response.json();
        createdTxIDs.push(body.txID);
        expect(body.amount).toBe(data.amount);
      } else if (response.status() === 500) {
        // Service unavailable - skip this test iteration
        console.log('Skipping multiple issuances test due to service unavailability');
        return;
      }
    }

    // Verify all issuances appear in status
    if (createdTxIDs.length > 0) {
      const statusResponse = await request.get(`/status/${testAddress}`, {
        headers: {
          'x-api-key': validApiKey
        }
      });

      expect(statusResponse.status()).toBe(200);
      const statusBody = await statusResponse.json();
      expect(Array.isArray(statusBody)).toBe(true);

      // Verify all created txIDs are present
      for (const txID of createdTxIDs) {
        const foundIssuance = statusBody.find(issuance => issuance.txID === txID);
        expect(foundIssuance).toBeDefined();
      }

      // Verify amounts are correct
      for (let i = 0; i < createdTxIDs.length; i++) {
        const issuance = statusBody.find(issuance => issuance.txID === createdTxIDs[i]);
        if (issuance) {
          expect(issuance.amount).toBe(issuanceData[i].amount);
        }
      }
    }
  });

  test('API rate limiting behavior', async ({ request }) => {
    // Test that the API handles multiple rapid requests appropriately
    const testAddress = 'TRATELIMIT123';
    const requests = [];

    // Make 5 rapid requests
    for (let i = 0; i < 5; i++) {
      requests.push(
        request.get(`/status/${testAddress}`, {
          headers: {
            'x-api-key': validApiKey
          }
        })
      );
    }

    const responses = await Promise.all(requests);

    // All requests should either succeed or be rate limited
    for (const response of responses) {
      expect([200, 429, 500]).toContain(response.status());
    }

    // At least some requests should succeed (200) or be rate limited (429)
    const successfulResponses = responses.filter(r => r.status() === 200);
    const rateLimitedResponses = responses.filter(r => r.status() === 429);
    expect(successfulResponses.length + rateLimitedResponses.length).toBeGreaterThan(0);
  });

  test('service health check integration', async ({ request }) => {
    // Verify health endpoint works and provides meaningful status
    const healthResponse = await request.get('/health');
    
    // Should be 200 or 429 (rate limited)
    expect([200, 429]).toContain(healthResponse.status());
    
    if (healthResponse.status() === 200) {
      const healthBody = await healthResponse.json();
      expect(healthBody).toHaveProperty('redis');
      expect(healthBody).toHaveProperty('tron');
      expect(healthBody.redis).toBe('PONG');
      expect(typeof healthBody.tron).toBe('boolean');

      // Health check should be consistently available
      const secondHealthResponse = await request.get('/health');
      expect([200, 429]).toContain(secondHealthResponse.status());
      
      if (secondHealthResponse.status() === 200) {
        const secondHealthBody = await secondHealthResponse.json();
        expect(secondHealthBody.redis).toBe('PONG');
      }
    }
  });
});