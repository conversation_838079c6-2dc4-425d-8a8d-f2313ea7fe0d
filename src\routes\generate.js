import express from 'express';
import <PERSON><PERSON> from 'joi';
import { tronWeb, mainAddress, networkConfig } from '../config/tron.js';
import redisClient from '../config/redis.js';
import logger from '../utils/logger.js';
import { getExplorerUrl } from '../config/network.js';

const router = express.Router();

const USDT_CONTRACT = networkConfig.usdtContract;
const RESERVE_RATIO = 50000; // 1 USDT on-chain backs 50,000 USDT off-chain
const MAX_SINGLE_ISSUANCE = 10000; // Maximum single off-chain issuance
const ISSUANCE_FEE_PERCENT = 0.1; // 0.1% fee for off-chain issuance

const schema = Joi.object({
  toAddress: Joi.string().required(),
  amount: Joi.number().positive().max(MAX_SINGLE_ISSUANCE).required(),
  validityMinutes: Joi.number().integer().min(5).max(172800).required()
});

// Helper function to get total USDT issuances
async function getTotalOffChainIssuances() {
  try {
    const keys = await redisClient.keys('issuance:*');
    let total = 0;

    for (const key of keys) {
      const record = await redisClient.get(key);
      if (record) {
        const data = JSON.parse(record);
        if (data.status === 'completed') {
          total += data.netAmount || data.amount;
        }
      }
    }

    return total;
  } catch (error) {
    logger.error('Error calculating total USDT issuances', { error: error.message });
    return 0;
  }
}

// Helper function to generate unique transaction ID
function generateOffChainTxID() {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  return `OFFCHAIN_${timestamp}_${random}`;
}

// POST /generate - Generate USDT tokens on Nile testnet (appears as real USDT in wallets)
router.post('/', async (req, res) => {
  const { error, value } = schema.validate(req.body);
  if (error) return res.status(400).json({ error: error.details[0].message });

  const { toAddress, amount, validityMinutes } = value;

  try {
    // Validate TRON address format
    if (!tronWeb.isAddress(toAddress)) {
      return res.status(400).json({ error: 'Invalid TRON address format' });
    }

    // Get main wallet USDT balance for reserve checking
    const contract = await tronWeb.contract().at(USDT_CONTRACT);
    const rawBalance = await contract.balanceOf(mainAddress).call();
    const mainWalletBalance = parseFloat(rawBalance.toString()) / 1000000;

    // Calculate maximum capacity based on reserve ratio
    const maxCapacity = mainWalletBalance * RESERVE_RATIO;

    // Get current total issued tokens
    const currentOffChainTotal = await getTotalOffChainIssuances();

    // Check if we have enough capacity for this issuance
    if (currentOffChainTotal + amount > maxCapacity) {
      return res.status(400).json({
        error: `Insufficient reserve capacity. Available: ${maxCapacity - currentOffChainTotal} USDT, Requested: ${amount} USDT`,
        reserve: {
          mainWalletBalance: `${mainWalletBalance} USDT`,
          maxCapacity: `${maxCapacity} USDT`,
          currentIssued: `${currentOffChainTotal} USDT`,
          remainingCapacity: `${maxCapacity - currentOffChainTotal} USDT`
        }
      });
    }

    // Calculate issuance fee
    const fee = amount * (ISSUANCE_FEE_PERCENT / 100);
    const netAmount = amount - fee;

    // Send real USDT tokens to the recipient address
    const amountSun = Math.floor(netAmount * 1000000); // Convert to 6 decimals

    // Send USDT tokens via blockchain transaction
    const txID = await contract.transfer(toAddress, amountSun).send({
      feeLimit: 100000000, // 100 TRX fee limit
      callValue: 0,
      shouldPollResponse: true
    });

    // Wait for transaction confirmation
    await new Promise(resolve => setTimeout(resolve, 3000));

    const issuedAt = new Date().toISOString();
    const expiresAt = new Date(Date.now() + validityMinutes * 60 * 1000).toISOString();

    // Create tracking record (for reserve monitoring)
    const issuanceRecord = {
      txID,
      type: 'usdt-issuance',
      toAddress,
      amount,
      netAmount,
      fee,
      status: 'completed',
      issuedAt,
      expiresAt,
      validityMinutes,
      reserveRatio: RESERVE_RATIO,
      onChainTransaction: true,
      note: "Real USDT tokens sent to wallet - appears as standard USDT"
    };

    // Store tracking record for reserve monitoring
    const key = `issuance:${toAddress}:${txID}`;
    await redisClient.setEx(key, validityMinutes * 60, JSON.stringify(issuanceRecord));

    // Store in global tracking for reserve monitoring
    const globalKey = `global:issuance:${txID}`;
    await redisClient.setEx(globalKey, validityMinutes * 60, JSON.stringify(issuanceRecord));

    logger.info('USDT tokens issued', {
      txID,
      toAddress,
      amount,
      netAmount,
      fee,
      validityMinutes,
      mainWalletBalance,
      maxCapacity,
      currentOffChainTotal: currentOffChainTotal + netAmount
    });

    // Calculate updated reserve status
    const newIssuedTotal = currentOffChainTotal + netAmount;
    const utilizationRate = (newIssuedTotal / maxCapacity) * 100;
    const remainingCapacity = maxCapacity - newIssuedTotal;

    res.json({
      success: true,
      txID,
      type: 'usdt-issuance',
      toAddress,
      amount,
      netAmount,
      fee: `${fee} USDT (${ISSUANCE_FEE_PERCENT}%)`,
      status: 'completed',
      issuedAt,
      expiresAt,
      validityMinutes,
      blockchain: {
        network: networkConfig.name,
        txID: txID,
        contractAddress: USDT_CONTRACT,
        explorerUrl: getExplorerUrl(txID),
        note: 'Real USDT tokens sent - will appear in wallet automatically'
      },
      reserve: {
        mainWalletBalance: `${mainWalletBalance} USDT`,
        reserveRatio: `1:${RESERVE_RATIO}`,
        maxCapacity: `${maxCapacity} USDT`,
        currentIssued: `${newIssuedTotal} USDT`,
        remainingCapacity: `${remainingCapacity} USDT`,
        utilizationRate: `${utilizationRate.toFixed(2)}%`
      },
      walletInfo: {
        tokenSymbol: "USDT",
        tokenName: "Tether USD",
        contractAddress: USDT_CONTRACT,
        decimals: 6,
        autoVisible: true,
        note: "Tokens appear as standard USDT in wallet - no custom token import needed"
      },
      capabilities: {
        standardUSDT: "Appears as regular USDT in any TRON wallet",
        externalTransfers: "Can send to any address with 1:50,000 conversion to reserve USDT",
        walletCompatible: "Works with TronLink, TronWallet, and all TRON wallets",
        noImportNeeded: "Automatically visible as USDT balance"
      },
      systemNote: "Fractional reserve system - issued tokens backed by main wallet reserves"
    });

  } catch (err) {
    logger.error('Off-chain issuance error', { error: err.message, stack: err.stack });
    res.status(500).json({ error: 'Failed to generate off-chain issuance', details: err.message });
  }
});

export default router;
