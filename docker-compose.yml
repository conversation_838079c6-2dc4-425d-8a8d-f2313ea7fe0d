version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - PORT=3000
      - TRON_FULL_NODE=${TRON_FULL_NODE}
      - TRON_SOLIDITY_NODE=${TRON_SOLIDITY_NODE}
      - TRON_EVENT_NODE=${TRON_EVENT_NODE}
      - MAIN_WALLET_PRIVATE_KEY=${MAIN_WALLET_PRIVATE_KEY}
      - USDT_CONTRACT_ADDRESS=${USDT_CONTRACT_ADDRESS}
      - REDIS_URL=${REDIS_URL}
      - API_KEY=${API_KEY}
    depends_on:
      - redis
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
